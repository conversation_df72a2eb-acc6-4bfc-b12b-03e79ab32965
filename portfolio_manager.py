#!/usr/bin/env python3
"""
Portfolio Manager - Integrates trading system with smart stop loss management
"""

import asyncio
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional
import pandas as pd
import numpy as np

from smart_stop_loss_system import SmartStopLossSystem, PositionInfo, StopLossOrder
from ibkr_client import IBKRClient
from config import IBKRConfig, DEFAULT_CONFIG

logger = logging.getLogger(__name__)

class PortfolioManager:
    """
    Manages portfolio positions and integrates with smart stop loss system
    """
    
    def __init__(self, bot, ibkr_config: IBKRConfig = None):
        self.bot = bot
        self.config = ibkr_config or DEFAULT_CONFIG
        self.stop_loss_system = SmartStopLossSystem(ibkr_config)
        self.ibkr_client = IBKRClient(self.config)
        
        # Portfolio tracking
        self.position_history = {}
        self.performance_metrics = {}
        
    async def connect(self) -> bool:
        """Connect to IBKR services"""
        ibkr_connected = await self.ibkr_client.connect()
        stop_loss_connected = await self.stop_loss_system.connect()
        return ibkr_connected and stop_loss_connected
    
    def disconnect(self):
        """Disconnect from IBKR services"""
        try:
            if hasattr(self.ibkr_client, 'disconnect'):
                self.ibkr_client.disconnect()
        except Exception as e:
            logger.warning(f"Error disconnecting IBKR client: {e}")

        try:
            if hasattr(self.stop_loss_system, 'disconnect'):
                self.stop_loss_system.disconnect()
        except Exception as e:
            logger.warning(f"Error disconnecting stop loss system: {e}")

        # Clear cached data to prevent memory leaks
        self.position_history.clear()
        self.performance_metrics.clear()
    
    def extract_positions_from_bot(self, market_data: Dict, correlation_data: Dict) -> List[PositionInfo]:
        """
        Extract current positions from bot and create PositionInfo objects
        """
        positions = []
        
        for ticker, position_data in self.bot.portfolio.items():
            if ticker in market_data:
                market_info = market_data[ticker]
                
                # Get correlation matches for this stock
                correlation_matches = self._get_correlation_matches(ticker, correlation_data)
                
                position = PositionInfo(
                    ticker=ticker,
                    units=position_data['units'],
                    purchase_price=position_data['purchase_price'],
                    current_price=market_info['current_price'],
                    sector=market_info.get('sector', 'Unknown'),
                    industry=market_info.get('industry', 'Unknown'),
                    trend_score=market_info.get('trend_score', 0.0),
                    predicted_price=market_info.get('predicted_price', market_info['current_price']),
                    volatility=market_info.get('volatility', 0.2),
                    correlation_matches=correlation_matches,
                    purchase_date=position_data.get('purchase_date', datetime.now())
                )
                positions.append(position)
                
                # Track position history
                if ticker not in self.position_history:
                    self.position_history[ticker] = []
                self.position_history[ticker].append(position)
        
        return positions
    
    def _get_correlation_matches(self, ticker: str, correlation_data: Dict) -> List[str]:
        """Get correlated stocks for a given ticker"""
        matches = []
        
        # From correlation analysis in daily trading system
        if 'correlation_opportunities' in correlation_data:
            for opportunity in correlation_data['correlation_opportunities']:
                # Check if ticker is in buy or sell candidates
                buy_tickers = [s['ticker'] for s in opportunity.get('buy_candidates', [])]
                sell_tickers = [s['ticker'] for s in opportunity.get('sell_candidates', [])]
                
                if ticker in buy_tickers:
                    matches.extend([t for t in buy_tickers + sell_tickers if t != ticker])
                elif ticker in sell_tickers:
                    matches.extend([t for t in buy_tickers + sell_tickers if t != ticker])
        
        # From sector groups
        if 'sector_groups' in correlation_data:
            for sector, stocks in correlation_data['sector_groups'].items():
                sector_tickers = [s['ticker'] for s in stocks]
                if ticker in sector_tickers:
                    matches.extend([t for t in sector_tickers if t != ticker])
        
        return list(set(matches))  # Remove duplicates
    
    async def manage_portfolio_risk(self, market_data: Dict, correlation_data: Dict) -> Dict:
        """
        Main function to manage portfolio risk using smart stop losses
        """
        logger.info("🛡️ Starting portfolio risk management...")
        
        try:
            # Extract current positions
            positions = self.extract_positions_from_bot(market_data, correlation_data)
            
            if not positions:
                logger.info("No positions to manage")
                return {"status": "no_positions", "orders": []}
            
            logger.info(f"Managing {len(positions)} positions")
            
            # Create/update stop loss orders
            stop_loss_orders = await self.stop_loss_system.create_stop_loss_orders(
                positions, correlation_data, market_data
            )
            
            # Update existing orders
            await self.stop_loss_system.update_stop_loss_orders(
                positions, correlation_data, market_data
            )
            
            # Calculate portfolio metrics
            portfolio_metrics = self._calculate_portfolio_metrics(positions, market_data)
            
            # Generate risk report
            risk_report = self._generate_risk_report(positions, stop_loss_orders, portfolio_metrics)
            
            logger.info("✅ Portfolio risk management completed")
            return risk_report
            
        except Exception as e:
            logger.error(f"Error in portfolio risk management: {e}")
            return {"status": "error", "error": str(e)}
    
    def _calculate_portfolio_metrics(self, positions: List[PositionInfo], market_data: Dict) -> Dict:
        """Calculate portfolio performance and risk metrics"""
        if not positions:
            return {}
        
        total_value = sum(pos.current_price * pos.units for pos in positions)
        total_cost = sum(pos.purchase_price * pos.units for pos in positions)
        total_pnl = total_value - total_cost
        total_pnl_pct = (total_pnl / total_cost) * 100 if total_cost > 0 else 0
        
        # Position-level metrics
        position_metrics = []
        for pos in positions:
            position_value = pos.current_price * pos.units
            position_cost = pos.purchase_price * pos.units
            position_pnl = position_value - position_cost
            position_pnl_pct = (position_pnl / position_cost) * 100 if position_cost > 0 else 0
            
            position_metrics.append({
                'ticker': pos.ticker,
                'value': position_value,
                'cost': position_cost,
                'pnl': position_pnl,
                'pnl_pct': position_pnl_pct,
                'weight': (position_value / total_value) * 100 if total_value > 0 else 0,
                'sector': pos.sector,
                'trend_score': pos.trend_score
            })
        
        # Sector diversification
        sector_exposure = {}
        for pos in positions:
            sector = pos.sector
            position_value = pos.current_price * pos.units
            sector_exposure[sector] = sector_exposure.get(sector, 0) + position_value
        
        sector_weights = {
            sector: (value / total_value) * 100 
            for sector, value in sector_exposure.items()
        } if total_value > 0 else {}
        
        return {
            'total_value': total_value,
            'total_cost': total_cost,
            'total_pnl': total_pnl,
            'total_pnl_pct': total_pnl_pct,
            'position_count': len(positions),
            'position_metrics': position_metrics,
            'sector_weights': sector_weights,
            'largest_position_weight': max([pm['weight'] for pm in position_metrics]) if position_metrics else 0,
            'avg_trend_score': np.mean([pos.trend_score for pos in positions])
        }
    
    def _generate_risk_report(self, positions: List[PositionInfo], 
                            stop_loss_orders: Dict[str, StopLossOrder], 
                            portfolio_metrics: Dict) -> Dict:
        """Generate comprehensive risk report"""
        
        # Risk assessment
        risk_level = "LOW"
        risk_factors = []
        
        # Check concentration risk
        if portfolio_metrics.get('largest_position_weight', 0) > 20:
            risk_level = "MEDIUM"
            risk_factors.append("High concentration in single position")
        
        # Check sector concentration
        max_sector_weight = max(portfolio_metrics.get('sector_weights', {}).values()) if portfolio_metrics.get('sector_weights') else 0
        if max_sector_weight > 40:
            risk_level = "HIGH" if risk_level != "HIGH" else risk_level
            risk_factors.append("High sector concentration")
        
        # Check trend alignment
        avg_trend = portfolio_metrics.get('avg_trend_score', 0)
        if avg_trend < -1.0:
            risk_level = "HIGH"
            risk_factors.append("Portfolio heavily below trend")
        
        # Check stop loss coverage
        stop_loss_coverage = len(stop_loss_orders) / len(positions) if positions else 0
        if stop_loss_coverage < 0.8:
            risk_factors.append("Insufficient stop loss coverage")
        
        return {
            'status': 'success',
            'timestamp': datetime.now(),
            'portfolio_metrics': portfolio_metrics,
            'risk_level': risk_level,
            'risk_factors': risk_factors,
            'stop_loss_orders': len(stop_loss_orders),
            'stop_loss_coverage': stop_loss_coverage,
            'positions': len(positions),
            'recommendations': self._generate_recommendations(risk_level, risk_factors, portfolio_metrics)
        }
    
    def _generate_recommendations(self, risk_level: str, risk_factors: List[str], 
                                portfolio_metrics: Dict) -> List[str]:
        """Generate actionable recommendations based on risk analysis"""
        recommendations = []
        
        if risk_level == "HIGH":
            recommendations.append("🚨 Consider reducing position sizes to manage risk")
            
        if "High concentration in single position" in risk_factors:
            recommendations.append("📊 Diversify by reducing largest position")
            
        if "High sector concentration" in risk_factors:
            recommendations.append("🏢 Consider diversifying across sectors")
            
        if "Portfolio heavily below trend" in risk_factors:
            recommendations.append("📉 Review positions - market may be turning bearish")
            
        if "Insufficient stop loss coverage" in risk_factors:
            recommendations.append("🛡️ Ensure all positions have stop loss protection")
        
        # Performance-based recommendations
        if portfolio_metrics.get('total_pnl_pct', 0) > 15:
            recommendations.append("💰 Consider taking profits on strong performers")
        elif portfolio_metrics.get('total_pnl_pct', 0) < -10:
            recommendations.append("⚠️ Review stop loss levels - consider tightening")
        
        if not recommendations:
            recommendations.append("✅ Portfolio risk appears well managed")
        
        return recommendations
    
    async def execute_risk_management_cycle(self, market_data: Dict, correlation_data: Dict) -> Dict:
        """
        Execute a complete risk management cycle
        """
        logger.info("🔄 Starting risk management cycle...")
        
        # Connect to services
        if not await self.connect():
            return {"status": "connection_failed"}
        
        try:
            # Manage portfolio risk
            risk_report = await self.manage_portfolio_risk(market_data, correlation_data)
            
            # Log summary
            if risk_report.get('status') == 'success':
                metrics = risk_report['portfolio_metrics']
                logger.info(f"📊 Portfolio Summary:")
                logger.info(f"  Total Value: ${metrics.get('total_value', 0):,.2f}")
                logger.info(f"  Total P&L: ${metrics.get('total_pnl', 0):,.2f} ({metrics.get('total_pnl_pct', 0):.1f}%)")
                logger.info(f"  Risk Level: {risk_report['risk_level']}")
                logger.info(f"  Stop Loss Coverage: {risk_report['stop_loss_coverage']:.1%}")
            
            return risk_report
            
        finally:
            self.disconnect()
    
    def get_portfolio_summary(self) -> Dict:
        """Get current portfolio summary"""
        return {
            'positions': len(self.bot.portfolio),
            'capital': self.bot.capital,
            'invested': self.bot.invested,
            'uninvested': self.bot.uninvested,
            'active_stop_losses': len(self.stop_loss_system.active_orders),
            'last_updated': datetime.now()
        }
