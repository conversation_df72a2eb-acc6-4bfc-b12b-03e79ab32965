# 🚀 智能交易系统 - 快速参考

## 📋 核心命令

### 🔧 系统启动
```bash
# 纸上交易（推荐新手）
python daily_trading_system.py --paper --bot Betty --capital 100000

# 实盘交易（谨慎使用）
python daily_trading_system.py --live --bot Betty --capital 50000

# 仅分析不交易
python daily_trading_system.py --analysis-only
```

### 📊 数据管理
```bash
# 更新股票池
python update_stock_universe.py

# 下载历史数据
python ibkr_data_provider.py

# 检查数据质量
python -c "from ibkr_data_provider import IBKRDataProvider; IBKRDataProvider().validate_data_integrity()"
```

### 🤖 模型训练
```bash
# 训练回归预测模型
python enhanced_reversion_prediction.py

# 模型性能比较
python model_comparison_test.py

# 简单模型测试
python simple_model_test.py
```

## ⚙️ 关键配置

### 🔌 IBKR连接
```python
# config.py
IBKRConfig(
    host="127.0.0.1",
    port=7497,              # 7497=纸上, 7496=实盘
    paper_trading=True,     # True=纸上, False=实盘
    timeout=30
)
```

### 💰 交易参数
```python
# 风险控制
max_position_size = 0.05        # 单股最大仓位 5%
stop_loss_threshold = 0.03      # 止损阈值 3%
take_profit_threshold = 0.1     # 止盈阈值 10%
max_sector_exposure = 0.3       # 单板块最大暴露 30%
```

### 🎯 趋势分类
```python
# 7级趋势分类（股票分布比例）
EXTREME_BELOW_TREND    # 0.1-0.6% 极度超卖 🔴
HIGHLY_BELOW_TREND     # 1-2%     高度超卖 🟠  
BELOW_TREND            # 5-8%     轻度超卖 🟡
ALONG_TREND            # 38-79%   正常趋势 🟢
ABOVE_TREND            # 5-8%     轻度超买 🟡
HIGHLY_ABOVE_TREND     # 1-2%     高度超买 🟠
EXTREME_ABOVE_TREND    # 0.1-0.6% 极度超买 🔴
```

## 🤖 交易机器人

### Adam - 保守型
- **策略**: 买入HIGHLY BELOW，卖出HIGHLY ABOVE
- **风险**: 低
- **适合**: 稳定收益

### Betty - 平衡型 ⭐
- **策略**: 动态止盈止损
- **止盈**: 10%
- **止损**: 3%
- **适合**: 大多数用户

### Chris - 大盘股
- **策略**: 专注FAANG股票
- **风险**: 低
- **适合**: 保守投资者

### Dany - 激进型
- **策略**: 快速交易，短期持有
- **风险**: 高
- **适合**: 经验丰富的交易者

### Eddy - 反向投资
- **策略**: 逆向思维
- **风险**: 高
- **适合**: 专业投资者

### Flora - 长期价值
- **策略**: 价值投资，长期持有
- **风险**: 低
- **适合**: 长期投资者

## 📁 重要文件

### 配置文件
- `config.py` - 主配置文件
- `.env` - 环境变量（需创建）

### 数据文件
- `data_store/stock_info.csv` - 股票信息
- `data_store/symbols_list.txt` - 股票代码列表
- `data_store/daily_model_cache_*.pickle` - 模型缓存

### 结果文件
- `results/YYYYMMDD/daily_results_*.csv` - 每日分析结果
- `results/YYYYMMDD/trading_summary_*.csv` - 交易汇总
- `results/YYYYMMDD/logs/` - 日志文件

## 🚨 安全检查清单

### 启动前检查
- [ ] IBKR TWS/Gateway已启动
- [ ] API连接已启用
- [ ] 端口配置正确（7497纸上/7496实盘）
- [ ] 账户权限充足
- [ ] 数据文件完整

### 交易前检查
- [ ] 确认交易模式（纸上/实盘）
- [ ] 检查资金量设置
- [ ] 验证止损止盈参数
- [ ] 确认机器人策略
- [ ] 检查网络连接稳定

### 运行中监控
- [ ] 监控连接状态
- [ ] 检查交易执行
- [ ] 观察风险指标
- [ ] 关注异常日志
- [ ] 验证仓位变化

## 🔧 故障排除

### 连接问题
```bash
# 检查IBKR连接
netstat -an | grep 7497

# 重启连接
python -c "
from ibkr_client import IBKRClient
import asyncio
async def test():
    client = IBKRClient()
    print(await client.connect())
asyncio.run(test())
"
```

### 数据问题
```bash
# 清理缓存
rm data_store/daily_model_cache_*.pickle

# 重新下载
python ibkr_data_provider.py --force-download
```

### 交易问题
```bash
# 检查账户状态
python -c "
from portfolio_manager import PortfolioManager
pm = PortfolioManager()
print(pm.get_portfolio_summary())
"
```

## 📊 监控命令

### 实时日志
```bash
# 查看交易日志
tail -f results/$(date +%Y%m%d)/logs/trading_*.log

# 查看错误日志
grep -i error results/*/logs/*.log | tail -10

# 查看连接状态
grep -i connect results/*/logs/*.log | tail -5
```

### 系统状态
```bash
# 检查进程
ps aux | grep -E "(python.*daily_trading|TWS)"

# 检查资源使用
top -p $(pgrep -f "python.*daily_trading")

# 检查磁盘空间
df -h data_store/ results/
```

## 🎯 最佳实践

### 新手建议
1. **从纸上交易开始** - 充分测试策略
2. **小额资金** - 实盘从小额开始
3. **选择Betty机器人** - 平衡的策略
4. **定期监控** - 每日检查结果
5. **保持学习** - 理解系统原理

### 进阶技巧
1. **多策略组合** - 运行多个机器人
2. **参数优化** - 根据市场调整参数
3. **风险管理** - 严格执行止损
4. **数据分析** - 分析历史表现
5. **持续改进** - 优化策略和参数

### 风险提醒
⚠️ **实盘交易有风险，投资需谨慎**
⚠️ **充分测试后再使用实盘模式**
⚠️ **设置合理的止损和仓位限制**
⚠️ **定期备份重要数据和配置**
⚠️ **遵守当地金融法规**

## 📞 技术支持

### 日志位置
- 交易日志: `results/YYYYMMDD/logs/trading_*.log`
- 系统日志: `results/YYYYMMDD/logs/system_*.log`
- 错误日志: `results/YYYYMMDD/logs/error_*.log`

### 配置文件
- 主配置: `config.py`
- 环境变量: `.env`
- 股票信息: `data_store/stock_info.csv`

### 常用检查
```bash
# 系统健康检查
python -c "
from daily_trading_system import DailyTradingSystem
system = DailyTradingSystem()
print('System Status:', system.health_check())
"

# 数据完整性检查
python -c "
import os
print('Stock info exists:', os.path.exists('data_store/stock_info.csv'))
print('Symbols list exists:', os.path.exists('data_store/symbols_list.txt'))
"
```

---
**记住**: 投资有风险，使用本系统前请充分了解相关风险并做好资金管理！
