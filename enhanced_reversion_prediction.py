#!/usr/bin/env python3
"""
增强版股票相关性背离回归时间预测模块

集成IBKR数据下载系统，使用真实的3年历史数据，
包含更丰富的特征工程（行业、板块、技术指标等）

主要功能：
1. 集成IBKR数据下载（3年历史数据）
2. 使用stock_info.csv中的行业板块分类
3. 增强的技术指标计算
4. 行业和板块相关性分析
5. 市场环境检测
6. LSTM模型训练和预测
7. 全面的回测验证

作者：基于reversion_prediction.py的增强版本
"""

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import pickle
import os
import asyncio
from datetime import datetime, timedelta
from typing import Dict, List, Tuple, Optional, NamedTuple
import logging
from dataclasses import dataclass, field
import warnings
warnings.filterwarnings('ignore')

# Deep learning imports
import tensorflow as tf
from tensorflow.keras.models import Sequential, Model
from tensorflow.keras.layers import (LSTM, GRU, Dense, Dropout, BatchNormalization,
                                   Conv1D, MaxPooling1D, Flatten, Concatenate, Input,
                                   GlobalMaxPooling1D, GlobalAveragePooling1D)
from tensorflow.keras.optimizers import Adam
from tensorflow.keras.callbacks import EarlyStopping, ReduceLROnPlateau
from sklearn.preprocessing import StandardScaler, MinMaxScaler, LabelEncoder
from sklearn.model_selection import train_test_split, TimeSeriesSplit
from sklearn.metrics import mean_squared_error, mean_absolute_error

# 导入现有的IBKR数据下载模块
from ibkr_data_provider import IBKRDataProvider
from ibkr_client import IBKRClient

# 导入缓存配置和数据质量管理
from cache_config import cache_config, filter_stocks_by_quality, get_min_bars_requirement

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Technical indicators - try to import talib, fallback to numpy implementations
try:
    import talib
    HAS_TALIB = True
except ImportError:
    HAS_TALIB = False
    logger.warning("TA-Lib not available, using numpy implementations for technical indicators")

@dataclass
class EnhancedDivergenceEvent:
    """增强的背离事件数据结构"""
    stock_a: str
    stock_b: str
    start_date: int  # 背离开始的索引
    end_date: Optional[int]  # 回归结束的索引
    max_divergence: float  # 最大背离幅度
    correlation: float  # 基础相关性
    reversion_days: Optional[int]  # 回归天数
    market_condition: str  # 市场环境
    
    # 增强特征
    sector_a: str = ""
    sector_b: str = ""
    industry_a: str = ""
    industry_b: str = ""
    same_sector: bool = False
    same_industry: bool = False
    market_cap_ratio: float = 1.0
    volatility_ratio: float = 1.0
    volume_ratio: float = 1.0
    
    # 技术指标特征
    rsi_divergence: float = 0.0
    macd_divergence: float = 0.0
    bb_position_divergence: float = 0.0
    momentum_divergence: float = 0.0

@dataclass
class MarketEnvironment:
    """市场环境数据结构"""
    date_index: int
    condition: str  # bull/bear/sideways
    volatility: float
    trend_strength: float
    volume_trend: float

class EnhancedReversionPredictor:
    """增强版背离回归时间预测器"""
    
    def __init__(self, config: Dict = None):
        """
        初始化增强预测器
        
        Parameters:
        -----------
        config: Dict
            配置参数
        """
        self.config = config or self._default_config()
        self.scaler_features = StandardScaler()
        self.scaler_target = MinMaxScaler()
        self.sector_encoder = LabelEncoder()
        self.industry_encoder = LabelEncoder()
        self.model = None
        self.divergence_events = []
        self.feature_columns = []
        self.stock_info = {}
        self.market_data = None
        
    def _default_config(self) -> Dict:
        """默认配置"""
        return {
            # 数据下载配置
            'data_duration_years': 3,  # 下载3年历史数据
            'include_volume': True,    # 包含成交量数据
            'include_technical_indicators': True,  # 包含技术指标
            
            # 相关性分析配置
            'correlation_threshold': 0.6,  # 降低阈值以获得更多股票对
            'divergence_threshold': 1.5,   # 背离阈值
            'reversion_threshold': 0.5,    # 回归阈值
            'min_divergence_days': 3,      # 最小背离持续天数
            'max_divergence_days': 120,    # 最大背离持续天数（4个月）
            
            # 技术指标配置
            'rsi_period': 14,
            'macd_fast': 12,
            'macd_slow': 26,
            'macd_signal': 9,
            'bb_period': 20,
            'bb_std': 2,
            'volume_ma_period': 20,
            'atr_period': 14,
            
            # 特征工程配置
            'include_sector_features': True,
            'include_industry_features': True,
            'include_market_environment': True,
            'include_cross_correlations': True,
            
            # 模型配置
            'model_type': 'cnn_gru',       # 'lstm', 'cnn_gru', 'hybrid'
            'cnn_filters': [64, 32],       # CNN滤波器数量
            'cnn_kernel_size': 3,          # CNN卷积核大小
            'gru_units': [128, 64],        # GRU单元数
            'lstm_units': [128, 64, 32],   # LSTM单元数（备选）
            'dropout_rate': 0.3,
            'learning_rate': 0.0005,
            'batch_size': 64,
            'epochs': 200,
            'validation_split': 0.2,
            'early_stopping_patience': 30,
            
            # 验证配置
            'use_time_series_split': True,
            'n_splits': 5,
            'test_size': 0.2,
        }
    
    def load_stock_info(self, stock_info_path: str = "data_store/stock_info.csv") -> Dict:
        """
        加载股票信息（行业、板块等）
        
        Parameters:
        -----------
        stock_info_path: str
            股票信息文件路径
            
        Returns:
        --------
        Dict: 股票信息字典
        """
        try:
            df = pd.read_csv(stock_info_path)
            
            stock_info = {}
            for _, row in df.iterrows():
                symbol = row['symbol']
                stock_info[symbol] = {
                    'sector': row.get('sector', 'Unknown'),
                    'industry': row.get('industry', 'Unknown'),
                    'subcategory': row.get('subcategory', 'Unknown'),
                    'long_name': row.get('long_name', symbol),
                    'currency': row.get('currency', 'USD')
                }
            
            logger.info(f"✅ Loaded stock info for {len(stock_info)} stocks")
            logger.info(f"📊 Sectors: {len(set(info['sector'] for info in stock_info.values()))}")
            logger.info(f"📊 Industries: {len(set(info['industry'] for info in stock_info.values()))}")
            
            self.stock_info = stock_info
            return stock_info
            
        except Exception as e:
            logger.error(f"Error loading stock info: {e}")
            return {}
    
    def get_stock_symbols(self, symbols_path: str = "data_store/symbols_list.txt") -> List[str]:
        """
        获取股票代码列表
        
        Parameters:
        -----------
        symbols_path: str
            股票代码文件路径
            
        Returns:
        --------
        List[str]: 股票代码列表
        """
        try:
            with open(symbols_path, 'r') as f:
                symbols = f.read().strip().split()
            
            # 过滤掉在stock_info中不存在的股票
            if self.stock_info:
                symbols = [s for s in symbols if s in self.stock_info]
            
            logger.info(f"✅ Loaded {len(symbols)} stock symbols")
            return symbols
            
        except Exception as e:
            logger.error(f"Error loading stock symbols: {e}")
            return []
    
    async def download_historical_data(self, symbols: List[str],
                                     duration_years: int = 5) -> Dict:
        """
        下载历史数据（使用新的缓存结构和数据质量过滤）

        Parameters:
        -----------
        symbols: List[str]
            股票代码列表
        duration_years: int
            数据年数

        Returns:
        --------
        Dict: 历史数据字典
        """
        logger.info(f"📊 Starting download of {duration_years}-year historical data for {len(symbols)} stocks")

        # 检查缓存
        cache_path = cache_config.get_stock_data_cache_path(duration_years)
        if os.path.exists(cache_path):
            try:
                with open(cache_path, 'rb') as f:
                    cached_data = pickle.load(f)

                # 检查缓存数据是否包含足够的股票
                if cached_data and 'tickers' in cached_data and len(cached_data['tickers']) > 0:
                    logger.info(f"📦 Found cached data for {len(cached_data['tickers'])} stocks")

                    # 应用数据质量过滤
                    logger.info(f"🔍 Applying data quality filter for {duration_years}-year duration...")
                    filtered_data, quality_report = filter_stocks_by_quality(cached_data, duration_years)
                    cache_config.print_quality_report(quality_report)

                    if len(filtered_data['tickers']) > 0:
                        logger.info(f"✅ Using cached data: {len(filtered_data['tickers'])} stocks passed quality check")
                        return filtered_data
                    else:
                        logger.warning("⚠️ No stocks passed quality check in cached data, downloading fresh data")
            except Exception as e:
                logger.warning(f"Failed to load cached data: {e}")

        try:
            # 初始化IBKR数据提供者
            data_provider = IBKRDataProvider()

            # 设置当前持续时间年数（用于数据提供者的质量过滤）
            data_provider._current_duration_years = duration_years

            # 计算日期范围
            end_date = datetime.now()
            start_date = end_date - timedelta(days=duration_years * 365)

            logger.info(f"📅 Date range: {start_date.strftime('%Y-%m-%d')} to {end_date.strftime('%Y-%m-%d')}")
            logger.info(f"📏 Minimum bars required: {get_min_bars_requirement(duration_years)}")

            # 下载数据 - 确保使用完整的年数据
            logger.info(f"🔄 Downloading data from {start_date.strftime('%Y-%m-%d')} to {end_date.strftime('%Y-%m-%d')}")
            logger.info(f"📊 Expected trading days: ~{duration_years * 252} days")

            data = await data_provider.download_multiple_stocks(
                tickers=symbols,
                start=start_date.strftime('%Y-%m-%d'),
                end=end_date.strftime('%Y-%m-%d'),
                max_concurrent=10,  # 控制并发数
                duration=f"{duration_years} Y",  # 明确指定持续时间
                bar_size="1 day",  # 确保是日线数据
                what_to_show="ADJUSTED_LAST"  # 使用调整后的价格
            )
            
            if data and 'price' in data:
                logger.info(f"✅ Downloaded data for {len(data['tickers'])} stocks")

                # 数据质量过滤已在IBKRDataProvider中完成
                # 这里只需要添加股票信息和确保数据格式正确

                # 添加股票信息
                if self.stock_info:
                    data['sectors'] = {ticker: self.stock_info.get(ticker, {}).get('sector', 'Unknown')
                                     for ticker in data['tickers']}
                    data['industries'] = {ticker: self.stock_info.get(ticker, {}).get('industry', 'Unknown')
                                        for ticker in data['tickers']}

                # 确保数据格式正确
                if hasattr(data['price'], 'shape'):
                    logger.info(f"📊 Final data shape: {data['price'].shape}")
                    logger.info(f"📅 Trading days: {data['price'].shape[1]}")
                else:
                    logger.info(f"📊 Data type: {type(data['price'])}, stocks: {len(data['price'])}")

                return data
            else:
                logger.error("Failed to download historical data")
                return None
                
        except Exception as e:
            logger.error(f"Error downloading historical data: {e}")
            import traceback
            logger.error(f"Traceback: {traceback.format_exc()}")
            return None
    
    def calculate_enhanced_technical_indicators(self, prices: np.ndarray, 
                                              volumes: np.ndarray = None) -> Dict:
        """
        计算增强的技术指标
        
        Parameters:
        -----------
        prices: np.ndarray
            价格序列
        volumes: np.ndarray
            成交量序列（可选）
            
        Returns:
        --------
        Dict: 技术指标字典
        """
        try:
            # Validate input data
            if prices is None or len(prices) == 0:
                logger.warning("Empty or None prices array")
                return {}

            if len(prices) < 20:  # Minimum data for meaningful indicators
                logger.warning(f"Insufficient price data: {len(prices)} points")
                return {}

            # Convert to float64 and handle NaN values
            prices = np.array(prices, dtype=np.float64)

            # Check for NaN or infinite values
            if np.any(np.isnan(prices)) or np.any(np.isinf(prices)):
                logger.warning("NaN or infinite values in price data")
                # Remove NaN values
                valid_mask = np.isfinite(prices)
                if np.sum(valid_mask) < len(prices) * 0.8:  # Less than 80% valid data
                    logger.error("Too many invalid price values")
                    return {}
                prices = prices[valid_mask]

            # Validate volumes if provided
            if volumes is not None:
                volumes = np.array(volumes, dtype=np.float64)
                if len(volumes) != len(prices):
                    logger.warning(f"Volume length ({len(volumes)}) doesn't match price length ({len(prices)})")
                    volumes = None

            if HAS_TALIB:
                return self._calculate_enhanced_with_talib(prices, volumes)
            else:
                return self._calculate_enhanced_with_numpy(prices, volumes)

        except Exception as e:
            logger.error(f"Error calculating enhanced technical indicators: {e}")
            import traceback
            logger.error(f"Traceback: {traceback.format_exc()}")
            return {}
    
    def _calculate_enhanced_with_talib(self, prices: np.ndarray, 
                                     volumes: np.ndarray = None) -> Dict:
        """使用TA-Lib计算增强技术指标"""
        indicators = {}
        
        # 基础技术指标
        indicators['rsi'] = talib.RSI(prices, timeperiod=self.config['rsi_period'])
        indicators['macd'], indicators['macd_signal'], indicators['macd_hist'] = talib.MACD(
            prices, 
            fastperiod=self.config['macd_fast'],
            slowperiod=self.config['macd_slow'], 
            signalperiod=self.config['macd_signal']
        )
        
        # 布林带
        indicators['bb_upper'], indicators['bb_middle'], indicators['bb_lower'] = talib.BBANDS(
            prices, timeperiod=self.config['bb_period'], nbdevup=self.config['bb_std'], 
            nbdevdn=self.config['bb_std']
        )
        indicators['bb_position'] = (prices - indicators['bb_lower']) / (
            indicators['bb_upper'] - indicators['bb_lower']
        )
        
        # 移动平均
        indicators['sma_20'] = talib.SMA(prices, timeperiod=20)
        indicators['sma_50'] = talib.SMA(prices, timeperiod=50)
        indicators['sma_200'] = talib.SMA(prices, timeperiod=200)
        indicators['ema_12'] = talib.EMA(prices, timeperiod=12)
        indicators['ema_26'] = talib.EMA(prices, timeperiod=26)
        
        # 动量指标
        indicators['momentum'] = talib.MOM(prices, timeperiod=10)
        indicators['roc'] = talib.ROC(prices, timeperiod=10)
        indicators['cci'] = talib.CCI(prices, prices, prices, timeperiod=14)
        indicators['williams_r'] = talib.WILLR(prices, prices, prices, timeperiod=14)
        
        # 波动率指标
        high = low = close = prices  # 简化处理
        indicators['atr'] = talib.ATR(high, low, close, timeperiod=self.config['atr_period'])
        indicators['volatility'] = indicators['atr'] / prices
        
        # 趋势指标
        indicators['adx'] = talib.ADX(high, low, close, timeperiod=14)
        indicators['aroon_up'], indicators['aroon_down'] = talib.AROON(high, low, timeperiod=14)
        
        # 成交量指标
        if volumes is not None:
            volumes = volumes.astype(np.float64)
            indicators['volume_sma'] = talib.SMA(volumes, timeperiod=self.config['volume_ma_period'])
            indicators['volume_ratio'] = volumes / indicators['volume_sma']
            indicators['obv'] = talib.OBV(close, volumes)
            indicators['ad'] = talib.AD(high, low, close, volumes)
        else:
            indicators['volume_ratio'] = np.ones_like(prices)
            indicators['obv'] = np.zeros_like(prices)
            indicators['ad'] = np.zeros_like(prices)
        
        return indicators

    def _calculate_enhanced_with_numpy(self, prices: np.ndarray,
                                     volumes: np.ndarray = None) -> Dict:
        """使用numpy计算增强技术指标"""
        def sma(data, window):
            """简单移动平均"""
            result = np.full_like(data, np.nan)
            for i in range(window-1, len(data)):
                result[i] = np.mean(data[i-window+1:i+1])
            return result

        def ema(data, window):
            """指数移动平均"""
            alpha = 2.0 / (window + 1.0)
            result = np.full_like(data, np.nan)
            result[0] = data[0]
            for i in range(1, len(data)):
                if not np.isnan(result[i-1]):
                    result[i] = alpha * data[i] + (1 - alpha) * result[i-1]
                else:
                    result[i] = data[i]
            return result

        def rsi(prices, window=14):
            """相对强弱指数"""
            deltas = np.diff(prices)
            gains = np.where(deltas > 0, deltas, 0)
            losses = np.where(deltas < 0, -deltas, 0)

            avg_gains = sma(np.concatenate([[0], gains]), window)[1:]
            avg_losses = sma(np.concatenate([[0], losses]), window)[1:]

            rs = avg_gains / (avg_losses + 1e-10)
            rsi_values = 100 - (100 / (1 + rs))
            return np.concatenate([[np.nan], rsi_values])

        def bollinger_bands(prices, window=20, num_std=2):
            """布林带"""
            sma_values = sma(prices, window)
            std_values = np.full_like(prices, np.nan)

            for i in range(window-1, len(prices)):
                std_values[i] = np.std(prices[i-window+1:i+1])

            upper = sma_values + (std_values * num_std)
            lower = sma_values - (std_values * num_std)

            return upper, sma_values, lower

        def macd(prices, fast=12, slow=26, signal=9):
            """MACD指标"""
            ema_fast = ema(prices, fast)
            ema_slow = ema(prices, slow)
            macd_line = ema_fast - ema_slow
            signal_line = ema(macd_line, signal)
            histogram = macd_line - signal_line

            return macd_line, signal_line, histogram

        def momentum(prices, window=10):
            """动量指标"""
            result = np.full_like(prices, np.nan)
            result[window:] = prices[window:] - prices[:-window]
            return result

        def roc(prices, window=10):
            """变化率"""
            result = np.full_like(prices, np.nan)
            result[window:] = (prices[window:] - prices[:-window]) / prices[:-window] * 100
            return result

        # 计算各种指标
        indicators = {}

        # 基础指标
        indicators['rsi'] = rsi(prices, self.config['rsi_period'])
        macd_line, macd_signal, macd_hist = macd(
            prices, self.config['macd_fast'], self.config['macd_slow'], self.config['macd_signal']
        )
        indicators['macd'] = macd_line
        indicators['macd_signal'] = macd_signal
        indicators['macd_hist'] = macd_hist

        # 布林带
        bb_upper, bb_middle, bb_lower = bollinger_bands(
            prices, self.config['bb_period'], self.config['bb_std']
        )
        indicators['bb_upper'] = bb_upper
        indicators['bb_middle'] = bb_middle
        indicators['bb_lower'] = bb_lower
        indicators['bb_position'] = np.where(
            (bb_upper - bb_lower) != 0,
            (prices - bb_lower) / (bb_upper - bb_lower),
            0.5
        )

        # 移动平均
        indicators['sma_20'] = sma(prices, 20)
        indicators['sma_50'] = sma(prices, 50)
        indicators['sma_200'] = sma(prices, 200)
        indicators['ema_12'] = ema(prices, 12)
        indicators['ema_26'] = ema(prices, 26)

        # 动量指标
        indicators['momentum'] = momentum(prices, 10)
        indicators['roc'] = roc(prices, 10)

        # 简化的CCI和Williams %R
        typical_price = prices  # 简化：使用收盘价
        indicators['cci'] = np.zeros_like(prices)  # 简化实现
        indicators['williams_r'] = np.zeros_like(prices)  # 简化实现

        # 波动率指标
        price_changes = np.abs(np.diff(prices))
        atr = sma(np.concatenate([[0], price_changes]), self.config['atr_period'])
        indicators['atr'] = atr
        indicators['volatility'] = atr / (prices + 1e-10)

        # 简化的趋势指标
        indicators['adx'] = np.zeros_like(prices)  # 简化实现
        indicators['aroon_up'] = np.zeros_like(prices)  # 简化实现
        indicators['aroon_down'] = np.zeros_like(prices)  # 简化实现

        # 成交量指标
        if volumes is not None:
            volumes = volumes.astype(np.float64)
            indicators['volume_sma'] = sma(volumes, self.config['volume_ma_period'])
            indicators['volume_ratio'] = np.where(
                indicators['volume_sma'] != 0,
                volumes / indicators['volume_sma'],
                1.0
            )

            # 简化的OBV
            price_changes = np.diff(prices)
            volume_direction = np.where(price_changes > 0, volumes[1:],
                                      np.where(price_changes < 0, -volumes[1:], 0))
            obv = np.cumsum(np.concatenate([[0], volume_direction]))
            indicators['obv'] = obv
            indicators['ad'] = obv  # 简化：使用OBV代替A/D线
        else:
            indicators['volume_ratio'] = np.ones_like(prices)
            indicators['obv'] = np.zeros_like(prices)
            indicators['ad'] = np.zeros_like(prices)

        return indicators

    def detect_market_environment(self, market_prices: np.ndarray,
                                window: int = 20) -> List[MarketEnvironment]:
        """
        检测市场环境

        Parameters:
        -----------
        market_prices: np.ndarray
            市场指数价格序列
        window: int
            检测窗口

        Returns:
        --------
        List[MarketEnvironment]: 市场环境列表
        """
        environments = []

        for i in range(len(market_prices)):
            if i < window:
                env = MarketEnvironment(
                    date_index=i,
                    condition='sideways',
                    volatility=0.02,
                    trend_strength=0.0,
                    volume_trend=0.0
                )
                environments.append(env)
                continue

            # 计算窗口内的统计数据
            window_prices = market_prices[i-window:i]

            # 趋势强度
            trend = (window_prices[-1] - window_prices[0]) / window_prices[0]

            # 波动率
            returns = np.diff(np.log(window_prices))
            volatility = np.std(returns) * np.sqrt(252)  # 年化波动率

            # 趋势强度（基于线性回归斜率）
            x = np.arange(len(window_prices))
            slope = np.polyfit(x, window_prices, 1)[0]
            trend_strength = slope / np.mean(window_prices)

            # 市场条件判断
            if trend > 0.1:  # 10%以上上涨
                condition = 'bull'
            elif trend < -0.1:  # 10%以上下跌
                condition = 'bear'
            else:
                condition = 'sideways'

            env = MarketEnvironment(
                date_index=i,
                condition=condition,
                volatility=volatility,
                trend_strength=trend_strength,
                volume_trend=0.0  # 简化
            )
            environments.append(env)

        return environments

    def calculate_correlation_matrix(self, price_data) -> np.ndarray:
        """
        计算股票间的相关性矩阵

        Parameters:
        -----------
        price_data: np.ndarray or list
            价格数据，形状为 (n_stocks, n_days) 或列表格式

        Returns:
        --------
        np.ndarray: 相关性矩阵
        """
        try:
            # 确保数据是numpy数组格式
            if isinstance(price_data, list):
                # 找到最短的时间序列长度
                min_length = min(len(prices) for prices in price_data if prices is not None)

                # 截断所有序列到相同长度
                aligned_data = []
                for prices in price_data:
                    if prices is not None and len(prices) >= min_length:
                        aligned_data.append(prices[-min_length:])
                    else:
                        # 用NaN填充无效数据
                        aligned_data.append([np.nan] * min_length)

                price_data = np.array(aligned_data)
                logger.info(f"📊 Aligned price data shape: {price_data.shape}")

            # 计算对数收益率
            log_returns = np.diff(np.log(price_data + 1e-10), axis=1)  # 添加小值避免log(0)

            # 移除包含NaN的行
            valid_mask = ~np.isnan(log_returns).any(axis=1)
            if np.sum(valid_mask) < len(log_returns) * 0.5:
                logger.warning(f"⚠️ Too many invalid stocks for correlation: {np.sum(valid_mask)}/{len(log_returns)}")
                return np.eye(len(log_returns))  # 返回单位矩阵

            valid_returns = log_returns[valid_mask]
            logger.info(f"📊 Valid returns for correlation: {valid_returns.shape}")

            # 计算相关性矩阵
            if valid_returns.shape[0] > 1:
                correlation_matrix = np.corrcoef(valid_returns)

                # 如果有无效股票，扩展矩阵
                if np.sum(valid_mask) < len(log_returns):
                    full_matrix = np.eye(len(log_returns))
                    valid_indices = np.where(valid_mask)[0]

                    for i, idx_i in enumerate(valid_indices):
                        for j, idx_j in enumerate(valid_indices):
                            full_matrix[idx_i, idx_j] = correlation_matrix[i, j]

                    correlation_matrix = full_matrix
            else:
                correlation_matrix = np.eye(len(log_returns))

            # 处理NaN值
            correlation_matrix = np.nan_to_num(correlation_matrix, nan=0.0)

            # 确保对角线为1
            np.fill_diagonal(correlation_matrix, 1.0)

            return correlation_matrix

        except Exception as e:
            logger.error(f"Error calculating correlation matrix: {e}")
            # 返回单位矩阵作为fallback
            n_stocks = len(price_data) if hasattr(price_data, '__len__') else 1
            return np.eye(n_stocks)

    def find_high_correlation_pairs(self, tickers: List[str],
                                  correlation_matrix: np.ndarray) -> Dict[str, List[Tuple[str, str, float]]]:
        """
        找到高相关性股票对，按相关性级别分类

        Parameters:
        -----------
        tickers: List[str]
            股票代码列表
        correlation_matrix: np.ndarray
            相关性矩阵

        Returns:
        --------
        Dict: 按相关性级别分类的股票对
        """
        correlation_groups = {
            'extreme_high': [],  # > 0.9
            'very_high': [],     # 0.85 - 0.9
            'high': [],          # 0.7 - 0.85
            'moderate': [],      # threshold - 0.7
            'all_pairs': []      # 所有高相关性对
        }

        n_stocks = len(tickers)
        threshold = self.config['correlation_threshold']
        total_pairs = 0

        for i in range(n_stocks):
            for j in range(i + 1, n_stocks):
                corr = correlation_matrix[i, j]
                abs_corr = abs(corr)

                if abs_corr >= threshold:
                    pair = (tickers[i], tickers[j], corr)
                    total_pairs += 1
                    correlation_groups['all_pairs'].append(pair)

                    if abs_corr > 0.9:
                        correlation_groups['extreme_high'].append(pair)
                    elif abs_corr > 0.85:
                        correlation_groups['very_high'].append(pair)
                    elif abs_corr > 0.7:
                        correlation_groups['high'].append(pair)
                    else:
                        correlation_groups['moderate'].append(pair)

        # 按相关性绝对值排序每个组
        for group in correlation_groups.values():
            group.sort(key=lambda x: abs(x[2]), reverse=True)

        logger.info(f"📊 Correlation Analysis Results:")
        logger.info(f"   🔥 Extreme High (>0.9): {len(correlation_groups['extreme_high'])} pairs")
        logger.info(f"   🔴 Very High (0.85-0.9): {len(correlation_groups['very_high'])} pairs")
        logger.info(f"   🟠 High (0.7-0.85): {len(correlation_groups['high'])} pairs")
        logger.info(f"   🟡 Moderate ({threshold}-0.7): {len(correlation_groups['moderate'])} pairs")
        logger.info(f"   📈 Total high correlation pairs: {total_pairs}")

        # 计算覆盖率
        total_possible = n_stocks * (n_stocks - 1) // 2
        coverage = total_pairs / total_possible * 100
        logger.info(f"   📊 Coverage: {coverage:.2f}% of all possible pairs")

        return correlation_groups

    def analyze_all_high_correlation_stocks(self, data: Dict, correlation_groups: Dict,
                                          market_environments: List) -> Dict:
        """
        分析所有高相关性股票的背离模式（您建议的方法）

        Parameters:
        -----------
        data: Dict
            股票数据
        correlation_groups: Dict
            相关性分组
        market_environments: List
            市场环境

        Returns:
        --------
        Dict: 全面的背离分析结果
        """
        logger.info("🚀 Analyzing ALL high correlation stocks for divergence patterns...")

        all_events = []
        all_features = []
        correlation_stats = {
            'extreme_high_events': 0,
            'very_high_events': 0,
            'high_events': 0,
            'moderate_events': 0,
            'total_pairs_analyzed': 0
        }

        # 分析每个相关性级别的股票对（智能采样）
        for level, pairs in correlation_groups.items():
            if level == 'all_pairs':
                continue

            # 智能采样：优先分析高相关性，限制低相关性数量
            if level == 'extreme_high':
                selected_pairs = pairs  # 全部分析
            elif level == 'very_high':
                selected_pairs = pairs[:500]  # 最多500对
            elif level == 'high':
                selected_pairs = pairs[:1000]  # 最多1000对
            else:  # moderate
                selected_pairs = pairs[:500]  # 最多500对

            logger.info(f"🔍 Analyzing {level} correlation pairs: {len(selected_pairs)}/{len(pairs)} pairs")

            for i, (ticker_a, ticker_b, correlation) in enumerate(selected_pairs):
                try:
                    # 获取价格数据
                    idx_a = data['tickers'].index(ticker_a)
                    idx_b = data['tickers'].index(ticker_b)
                    prices_a = data['price'][idx_a]
                    prices_b = data['price'][idx_b]

                    # 检查数据长度并对齐
                    min_length = min(len(prices_a), len(prices_b))
                    if min_length < 100:  # 跳过数据太少的股票对
                        continue

                    prices_a = prices_a[-min_length:]  # 取最后min_length个数据点
                    prices_b = prices_b[-min_length:]

                    # 获取成交量数据并对齐
                    volumes_a = data.get('volume', [None] * len(data['tickers']))[idx_a]
                    volumes_b = data.get('volume', [None] * len(data['tickers']))[idx_b]

                    if volumes_a is not None and volumes_b is not None:
                        volumes_a = volumes_a[-min_length:]
                        volumes_b = volumes_b[-min_length:]

                    # 计算增强技术指标
                    indicators_a = self.calculate_enhanced_technical_indicators(prices_a, volumes_a)
                    indicators_b = self.calculate_enhanced_technical_indicators(prices_b, volumes_b)

                    # 检测增强背离事件
                    events = self.detect_enhanced_divergence_events(
                        prices_a, prices_b, indicators_a, indicators_b,
                        correlation, (ticker_a, ticker_b), market_environments
                    )

                    # 为每个事件提取增强特征
                    for event in events:
                        features = self.extract_enhanced_features(event)
                        if features:
                            all_events.append(event)
                            all_features.append(features)

                            # 统计不同相关性级别的事件
                            abs_corr = abs(correlation)
                            if abs_corr > 0.9:
                                correlation_stats['extreme_high_events'] += 1
                            elif abs_corr > 0.85:
                                correlation_stats['very_high_events'] += 1
                            elif abs_corr > 0.7:
                                correlation_stats['high_events'] += 1
                            else:
                                correlation_stats['moderate_events'] += 1

                    correlation_stats['total_pairs_analyzed'] += 1

                    # 进度报告
                    if (i + 1) % 100 == 0:
                        logger.info(f"   Progress: {i + 1}/{len(selected_pairs)} pairs processed")

                except Exception as e:
                    logger.warning(f"Error processing pair {ticker_a}-{ticker_b}: {e}")
                    continue

        logger.info(f"🎯 Comprehensive Analysis Results:")
        logger.info(f"   📊 Total pairs analyzed: {correlation_stats['total_pairs_analyzed']}")
        logger.info(f"   🔥 Extreme high correlation events: {correlation_stats['extreme_high_events']}")
        logger.info(f"   🔴 Very high correlation events: {correlation_stats['very_high_events']}")
        logger.info(f"   🟠 High correlation events: {correlation_stats['high_events']}")
        logger.info(f"   🟡 Moderate correlation events: {correlation_stats['moderate_events']}")
        logger.info(f"   ✅ Total divergence events found: {len(all_events)}")

        return {
            'events': all_events,
            'features': all_features,
            'correlation_stats': correlation_stats,
            'correlation_groups': correlation_groups
        }

    def detect_enhanced_divergence_events(self, stock_a_prices: np.ndarray, stock_b_prices: np.ndarray,
                                        stock_a_indicators: Dict, stock_b_indicators: Dict,
                                        correlation: float, tickers: Tuple[str, str],
                                        market_environments: List[MarketEnvironment]) -> List[EnhancedDivergenceEvent]:
        """
        检测增强的背离事件

        Parameters:
        -----------
        stock_a_prices: np.ndarray
            股票A的价格序列
        stock_b_prices: np.ndarray
            股票B的价格序列
        stock_a_indicators: Dict
            股票A的技术指标
        stock_b_indicators: Dict
            股票B的技术指标
        correlation: float
            两股票的相关性
        tickers: Tuple[str, str]
            股票代码对
        market_environments: List[MarketEnvironment]
            市场环境列表

        Returns:
        --------
        List[EnhancedDivergenceEvent]: 增强背离事件列表
        """
        events = []

        # 计算标准化价格（Z-score）
        def normalize_prices(prices):
            returns = np.diff(np.log(prices))
            mean_return = np.mean(returns)
            std_return = np.std(returns)

            # 计算累积标准化收益
            normalized = np.cumsum((returns - mean_return) / std_return)
            return np.concatenate([[0], normalized])

        norm_a = normalize_prices(stock_a_prices)
        norm_b = normalize_prices(stock_b_prices)

        # 计算背离度（考虑相关性方向）
        if correlation > 0:
            divergence = np.abs(norm_a - norm_b)
        else:
            divergence = np.abs(norm_a + norm_b)

        # 检测背离事件
        threshold = self.config['divergence_threshold']
        in_divergence = False
        start_idx = None
        max_div = 0

        for i in range(len(divergence)):
            if not in_divergence and divergence[i] > threshold:
                # 背离开始
                in_divergence = True
                start_idx = i
                max_div = divergence[i]

            elif in_divergence:
                # 更新最大背离
                max_div = max(max_div, divergence[i])

                # 检查是否回归
                if divergence[i] < self.config['reversion_threshold']:
                    # 背离结束，创建增强事件
                    duration = i - start_idx
                    if duration >= self.config['min_divergence_days']:
                        event = self._create_enhanced_event(
                            tickers, start_idx, i, max_div, correlation, duration,
                            stock_a_indicators, stock_b_indicators, market_environments
                        )
                        if event:
                            events.append(event)

                    in_divergence = False
                    start_idx = None
                    max_div = 0

        # 处理未结束的背离
        if in_divergence and start_idx is not None:
            duration = len(divergence) - start_idx
            if duration >= self.config['min_divergence_days']:
                event = self._create_enhanced_event(
                    tickers, start_idx, None, max_div, correlation, None,
                    stock_a_indicators, stock_b_indicators, market_environments
                )
                if event:
                    events.append(event)

        return events

    def _create_enhanced_event(self, tickers: Tuple[str, str], start_idx: int, end_idx: Optional[int],
                             max_divergence: float, correlation: float, reversion_days: Optional[int],
                             indicators_a: Dict, indicators_b: Dict,
                             market_environments: List[MarketEnvironment]) -> Optional[EnhancedDivergenceEvent]:
        """创建增强背离事件"""
        try:
            ticker_a, ticker_b = tickers

            # 获取股票信息
            info_a = self.stock_info.get(ticker_a, {})
            info_b = self.stock_info.get(ticker_b, {})

            sector_a = info_a.get('sector', 'Unknown')
            sector_b = info_b.get('sector', 'Unknown')
            industry_a = info_a.get('industry', 'Unknown')
            industry_b = info_b.get('industry', 'Unknown')

            # 获取市场环境
            market_condition = 'sideways'
            if start_idx < len(market_environments):
                market_condition = market_environments[start_idx].condition

            # 计算技术指标背离
            rsi_a = indicators_a.get('rsi', [np.nan] * 1000)[start_idx] if start_idx < len(indicators_a.get('rsi', [])) else np.nan
            rsi_b = indicators_b.get('rsi', [np.nan] * 1000)[start_idx] if start_idx < len(indicators_b.get('rsi', [])) else np.nan
            rsi_divergence = abs(rsi_a - rsi_b) if not (np.isnan(rsi_a) or np.isnan(rsi_b)) else 0.0

            macd_a = indicators_a.get('macd', [np.nan] * 1000)[start_idx] if start_idx < len(indicators_a.get('macd', [])) else np.nan
            macd_b = indicators_b.get('macd', [np.nan] * 1000)[start_idx] if start_idx < len(indicators_b.get('macd', [])) else np.nan
            macd_divergence = abs(macd_a - macd_b) if not (np.isnan(macd_a) or np.isnan(macd_b)) else 0.0

            bb_pos_a = indicators_a.get('bb_position', [np.nan] * 1000)[start_idx] if start_idx < len(indicators_a.get('bb_position', [])) else np.nan
            bb_pos_b = indicators_b.get('bb_position', [np.nan] * 1000)[start_idx] if start_idx < len(indicators_b.get('bb_position', [])) else np.nan
            bb_position_divergence = abs(bb_pos_a - bb_pos_b) if not (np.isnan(bb_pos_a) or np.isnan(bb_pos_b)) else 0.0

            momentum_a = indicators_a.get('momentum', [np.nan] * 1000)[start_idx] if start_idx < len(indicators_a.get('momentum', [])) else np.nan
            momentum_b = indicators_b.get('momentum', [np.nan] * 1000)[start_idx] if start_idx < len(indicators_b.get('momentum', [])) else np.nan
            momentum_divergence = abs(momentum_a - momentum_b) if not (np.isnan(momentum_a) or np.isnan(momentum_b)) else 0.0

            # 创建增强事件
            event = EnhancedDivergenceEvent(
                stock_a=ticker_a,
                stock_b=ticker_b,
                start_date=start_idx,
                end_date=end_idx,
                max_divergence=max_divergence,
                correlation=correlation,
                reversion_days=reversion_days,
                market_condition=market_condition,
                sector_a=sector_a,
                sector_b=sector_b,
                industry_a=industry_a,
                industry_b=industry_b,
                same_sector=(sector_a == sector_b and sector_a != 'Unknown'),
                same_industry=(industry_a == industry_b and industry_a != 'Unknown'),
                rsi_divergence=rsi_divergence,
                macd_divergence=macd_divergence,
                bb_position_divergence=bb_position_divergence,
                momentum_divergence=momentum_divergence
            )

            return event

        except Exception as e:
            logger.error(f"Error creating enhanced event: {e}")
            return None

    def extract_enhanced_features(self, event: EnhancedDivergenceEvent) -> Dict:
        """
        提取增强特征

        Parameters:
        -----------
        event: EnhancedDivergenceEvent
            增强背离事件

        Returns:
        --------
        Dict: 增强特征字典
        """
        try:
            features = {
                # 基础特征
                'correlation': event.correlation,
                'max_divergence': event.max_divergence,
                'abs_correlation': abs(event.correlation),
                'correlation_strength': abs(event.correlation) ** 2,

                # 市场环境特征
                'market_bull': 1 if event.market_condition == 'bull' else 0,
                'market_bear': 1 if event.market_condition == 'bear' else 0,
                'market_sideways': 1 if event.market_condition == 'sideways' else 0,

                # 行业板块特征
                'same_sector': 1 if event.same_sector else 0,
                'same_industry': 1 if event.same_industry else 0,
                'cross_sector': 1 if not event.same_sector and event.sector_a != 'Unknown' and event.sector_b != 'Unknown' else 0,
                'cross_industry': 1 if not event.same_industry and event.industry_a != 'Unknown' and event.industry_b != 'Unknown' else 0,

                # 技术指标背离特征
                'rsi_divergence': event.rsi_divergence,
                'macd_divergence': event.macd_divergence,
                'bb_position_divergence': event.bb_position_divergence,
                'momentum_divergence': event.momentum_divergence,

                # 组合特征
                'technical_divergence_score': (
                    event.rsi_divergence + event.macd_divergence +
                    event.bb_position_divergence + event.momentum_divergence
                ) / 4.0,

                # 相关性类型特征
                'positive_correlation': 1 if event.correlation > 0 else 0,
                'negative_correlation': 1 if event.correlation < 0 else 0,
                'strong_correlation': 1 if abs(event.correlation) > 0.8 else 0,
                'moderate_correlation': 1 if 0.6 <= abs(event.correlation) <= 0.8 else 0,

                # 背离强度特征
                'high_divergence': 1 if event.max_divergence > 3.0 else 0,
                'medium_divergence': 1 if 2.0 <= event.max_divergence <= 3.0 else 0,
                'low_divergence': 1 if event.max_divergence < 2.0 else 0,
            }

            # 处理NaN值
            for key, value in features.items():
                if np.isnan(value) or np.isinf(value):
                    features[key] = 0.0

            return features

        except Exception as e:
            logger.error(f"Error extracting enhanced features: {e}")
            return {}

    def build_cnn_gru_model(self, input_shape: Tuple[int, int],
                           static_features_dim: int = 0) -> tf.keras.Model:
        """
        构建CNN-GRU混合模型

        Parameters:
        -----------
        input_shape: Tuple[int, int]
            时序输入形状 (sequence_length, n_features)
        static_features_dim: int
            静态特征维度

        Returns:
        --------
        tf.keras.Model: CNN-GRU混合模型
        """
        # 时序特征输入
        sequence_input = Input(shape=input_shape, name='sequence_input')

        # CNN层提取局部特征
        x = Conv1D(filters=self.config['cnn_filters'][0],
                  kernel_size=self.config['cnn_kernel_size'],
                  activation='relu', padding='same')(sequence_input)
        x = BatchNormalization()(x)
        x = Dropout(self.config['dropout_rate'])(x)

        if len(self.config['cnn_filters']) > 1:
            x = Conv1D(filters=self.config['cnn_filters'][1],
                      kernel_size=self.config['cnn_kernel_size'],
                      activation='relu', padding='same')(x)
            x = BatchNormalization()(x)
            x = Dropout(self.config['dropout_rate'])(x)

        # GRU层处理时序依赖
        for i, units in enumerate(self.config['gru_units']):
            return_sequences = i < len(self.config['gru_units']) - 1
            x = GRU(units=units, return_sequences=return_sequences,
                   dropout=self.config['dropout_rate'])(x)
            x = BatchNormalization()(x)

        # 时序特征输出
        sequence_output = x

        # 如果有静态特征，进行融合
        if static_features_dim > 0:
            static_input = Input(shape=(static_features_dim,), name='static_input')
            static_dense = Dense(64, activation='relu')(static_input)
            static_dense = BatchNormalization()(static_dense)
            static_dense = Dropout(self.config['dropout_rate'])(static_dense)

            # 融合时序和静态特征
            combined = Concatenate()([sequence_output, static_dense])
            inputs = [sequence_input, static_input]
        else:
            combined = sequence_output
            inputs = sequence_input

        # 全连接层
        x = Dense(128, activation='relu')(combined)
        x = BatchNormalization()(x)
        x = Dropout(self.config['dropout_rate'])(x)

        x = Dense(64, activation='relu')(x)
        x = BatchNormalization()(x)
        x = Dropout(self.config['dropout_rate'])(x)

        x = Dense(32, activation='relu')(x)
        x = Dropout(self.config['dropout_rate'])(x)

        # 输出层 - 预测回归天数
        output = Dense(1, activation='linear', name='reversion_days')(x)

        # 创建模型
        model = Model(inputs=inputs, outputs=output)

        # 编译模型
        model.compile(
            optimizer=Adam(learning_rate=self.config['learning_rate']),
            loss='mse',
            metrics=['mae']
        )

        return model

    def build_lstm_model(self, input_shape: Tuple[int, int]) -> tf.keras.Model:
        """
        构建传统LSTM模型（备选方案）

        Parameters:
        -----------
        input_shape: Tuple[int, int]
            输入形状 (sequence_length, n_features)

        Returns:
        --------
        tf.keras.Model: LSTM模型
        """
        model = Sequential()

        # LSTM层
        for i, units in enumerate(self.config['lstm_units']):
            return_sequences = i < len(self.config['lstm_units']) - 1
            if i == 0:
                model.add(LSTM(units=units, return_sequences=return_sequences,
                             input_shape=input_shape))
            else:
                model.add(LSTM(units=units, return_sequences=return_sequences))
            model.add(BatchNormalization())
            model.add(Dropout(self.config['dropout_rate']))

        # 全连接层
        model.add(Dense(64, activation='relu'))
        model.add(BatchNormalization())
        model.add(Dropout(self.config['dropout_rate']))

        model.add(Dense(32, activation='relu'))
        model.add(Dropout(self.config['dropout_rate']))

        # 输出层
        model.add(Dense(1, activation='linear'))

        # 编译模型
        model.compile(
            optimizer=Adam(learning_rate=self.config['learning_rate']),
            loss='mse',
            metrics=['mae']
        )

        return model

    def prepare_model_data(self, events: List[EnhancedDivergenceEvent],
                          features: List[Dict]) -> Tuple[np.ndarray, np.ndarray, np.ndarray]:
        """
        准备模型训练数据

        Parameters:
        -----------
        events: List[EnhancedDivergenceEvent]
            背离事件列表
        features: List[Dict]
            特征列表

        Returns:
        --------
        Tuple[np.ndarray, np.ndarray, np.ndarray]: (时序特征, 静态特征, 目标值)
        """
        # 过滤出已回归的事件
        completed_events = []
        completed_features = []

        for event, feature_dict in zip(events, features):
            if event.reversion_days is not None and feature_dict:
                completed_events.append(event)
                completed_features.append(feature_dict)

        if not completed_events:
            logger.error("No completed reversion events found for training")
            return None, None, None

        logger.info(f"Found {len(completed_events)} completed events for training")

        # 分离时序特征和静态特征
        time_series_features = []
        static_features = []
        targets = []

        # 定义时序特征名称（技术指标相关）
        time_series_feature_names = [
            'rsi_divergence', 'macd_divergence', 'bb_position_divergence',
            'momentum_divergence', 'technical_divergence_score'
        ]

        # 定义静态特征名称
        static_feature_names = [
            'correlation', 'max_divergence', 'abs_correlation', 'correlation_strength',
            'market_bull', 'market_bear', 'market_sideways',
            'same_sector', 'same_industry', 'cross_sector', 'cross_industry',
            'positive_correlation', 'negative_correlation', 'strong_correlation', 'moderate_correlation',
            'high_divergence', 'medium_divergence', 'low_divergence'
        ]

        for event, feature_dict in zip(completed_events, completed_features):
            # 时序特征（简化为单个时间点，实际可以扩展为序列）
            ts_features = [feature_dict.get(name, 0.0) for name in time_series_feature_names]
            time_series_features.append([ts_features])  # 添加时间维度

            # 静态特征
            static_feat = [feature_dict.get(name, 0.0) for name in static_feature_names]
            static_features.append(static_feat)

            # 目标值：回归天数（对数变换）
            reversion_days = min(event.reversion_days, self.config['max_divergence_days'])
            targets.append(np.log1p(reversion_days))

        time_series_features = np.array(time_series_features)
        static_features = np.array(static_features)
        targets = np.array(targets)

        logger.info(f"Data shapes: time_series={time_series_features.shape}, "
                   f"static={static_features.shape}, targets={targets.shape}")

        return time_series_features, static_features, targets

    def train_enhanced_model(self, time_series_data: np.ndarray,
                           static_data: np.ndarray, targets: np.ndarray) -> Dict:
        """
        训练增强模型

        Parameters:
        -----------
        time_series_data: np.ndarray
            时序特征数据
        static_data: np.ndarray
            静态特征数据
        targets: np.ndarray
            目标数据

        Returns:
        --------
        Dict: 训练结果
        """
        # 数据标准化
        ts_scaled = self.scaler_features.fit_transform(
            time_series_data.reshape(-1, time_series_data.shape[-1])
        ).reshape(time_series_data.shape)

        static_scaled = StandardScaler().fit_transform(static_data)
        targets_scaled = self.scaler_target.fit_transform(targets.reshape(-1, 1)).flatten()

        # 分割训练和验证集
        if self.config['use_time_series_split']:
            # 使用时间序列分割
            from sklearn.model_selection import TimeSeriesSplit
            tscv = TimeSeriesSplit(n_splits=self.config['n_splits'])
            splits = list(tscv.split(ts_scaled))
            train_idx, val_idx = splits[-1]  # 使用最后一个分割
        else:
            # 使用随机分割
            train_idx, val_idx = train_test_split(
                range(len(ts_scaled)),
                test_size=self.config['validation_split'],
                random_state=42
            )

        X_ts_train, X_ts_val = ts_scaled[train_idx], ts_scaled[val_idx]
        X_static_train, X_static_val = static_scaled[train_idx], static_scaled[val_idx]
        y_train, y_val = targets_scaled[train_idx], targets_scaled[val_idx]

        logger.info(f"Training set: {len(train_idx)} samples")
        logger.info(f"Validation set: {len(val_idx)} samples")

        # 构建模型
        if self.config['model_type'] == 'cnn_gru':
            self.model = self.build_cnn_gru_model(
                input_shape=(X_ts_train.shape[1], X_ts_train.shape[2]),
                static_features_dim=X_static_train.shape[1]
            )
            train_data = [X_ts_train, X_static_train]
            val_data = [X_ts_val, X_static_val]
        else:  # lstm
            # 合并时序和静态特征
            X_combined_train = np.concatenate([
                X_ts_train.reshape(X_ts_train.shape[0], -1, 1),
                X_static_train.reshape(X_static_train.shape[0], 1, -1).repeat(X_ts_train.shape[1], axis=1)
            ], axis=2)
            X_combined_val = np.concatenate([
                X_ts_val.reshape(X_ts_val.shape[0], -1, 1),
                X_static_val.reshape(X_static_val.shape[0], 1, -1).repeat(X_ts_val.shape[1], axis=1)
            ], axis=2)

            self.model = self.build_lstm_model(
                input_shape=(X_combined_train.shape[1], X_combined_train.shape[2])
            )
            train_data = X_combined_train
            val_data = X_combined_val

        logger.info(f"Model architecture ({self.config['model_type']}):")
        self.model.summary()

        # 回调函数
        callbacks = [
            EarlyStopping(
                monitor='val_loss',
                patience=self.config['early_stopping_patience'],
                restore_best_weights=True,
                verbose=1
            ),
            ReduceLROnPlateau(
                monitor='val_loss',
                factor=0.5,
                patience=15,
                min_lr=1e-6,
                verbose=1
            )
        ]

        # 训练模型
        logger.info("Starting enhanced model training...")
        history = self.model.fit(
            train_data, y_train,
            validation_data=(val_data, y_val),
            epochs=self.config['epochs'],
            batch_size=self.config['batch_size'],
            callbacks=callbacks,
            verbose=1
        )

        # 评估模型
        if self.config['model_type'] == 'cnn_gru':
            train_pred = self.model.predict([X_ts_train, X_static_train])
            val_pred = self.model.predict([X_ts_val, X_static_val])
        else:
            train_pred = self.model.predict(X_combined_train)
            val_pred = self.model.predict(X_combined_val)

        # 反标准化预测结果
        train_pred_orig = self.scaler_target.inverse_transform(train_pred.reshape(-1, 1)).flatten()
        val_pred_orig = self.scaler_target.inverse_transform(val_pred.reshape(-1, 1)).flatten()
        y_train_orig = self.scaler_target.inverse_transform(y_train.reshape(-1, 1)).flatten()
        y_val_orig = self.scaler_target.inverse_transform(y_val.reshape(-1, 1)).flatten()

        # 转换回天数
        train_pred_days = np.expm1(train_pred_orig)
        val_pred_days = np.expm1(val_pred_orig)
        y_train_days = np.expm1(y_train_orig)
        y_val_days = np.expm1(y_val_orig)

        # 计算评估指标
        train_mse = mean_squared_error(y_train_days, train_pred_days)
        val_mse = mean_squared_error(y_val_days, val_pred_days)
        train_mae = mean_absolute_error(y_train_days, train_pred_days)
        val_mae = mean_absolute_error(y_val_days, val_pred_days)

        results = {
            'model_type': self.config['model_type'],
            'history': history.history,
            'train_mse': train_mse,
            'val_mse': val_mse,
            'train_mae': train_mae,
            'val_mae': val_mae,
            'train_predictions': train_pred_days,
            'val_predictions': val_pred_days,
            'train_actual': y_train_days,
            'val_actual': y_val_days
        }

        logger.info(f"Enhanced model training completed!")
        logger.info(f"Model type: {self.config['model_type']}")
        logger.info(f"Training MAE: {train_mae:.2f} days")
        logger.info(f"Validation MAE: {val_mae:.2f} days")

        return results

    def save_enhanced_model(self, model_path: str = None, strategy: str = "moderate",
                           extreme_level: str = "medium", duration_years: int = 5, actual_data_years: int = None):
        """保存增强模型到新的缓存结构"""
        if self.model is None:
            logger.error("No model to save")
            return

        # 使用实际数据年数
        if actual_data_years is None:
            actual_data_years = duration_years

        # 如果没有指定路径，使用新的缓存结构
        if model_path is None:
            model_type = self.config.get('model_type', 'cnn_gru')
            model_filename = f"model_{model_type}_{strategy}_{extreme_level}_{actual_data_years}y"
            model_path = cache_config.get_model_cache_path(model_filename, actual_data_years)
            # 移除扩展名，因为我们会添加不同的扩展名
            model_path = model_path.replace('.pkl', '')

        # 确保目录存在
        os.makedirs(os.path.dirname(model_path), exist_ok=True)

        # 保存模型
        self.model.save(f"{model_path}_enhanced_model.h5")

        # 保存标准化器和配置
        import joblib
        joblib.dump(self.scaler_features, f"{model_path}_scaler_features.pkl")
        joblib.dump(self.scaler_target, f"{model_path}_scaler_target.pkl")
        joblib.dump(self.config, f"{model_path}_config.pkl")

        logger.info(f"✅ Enhanced model saved to cache/model_cache/")
        logger.info(f"   Model: {model_path}_enhanced_model.h5")
        logger.info(f"   Scalers: {model_path}_scaler_*.pkl")
        logger.info(f"   Config: {model_path}_config.pkl")

    def load_enhanced_model(self, model_path: str):
        """加载增强模型"""
        try:
            import joblib

            # 加载模型
            self.model = tf.keras.models.load_model(f"{model_path}_enhanced_model.h5")

            # 加载标准化器和配置
            self.scaler_features = joblib.load(f"{model_path}_scaler_features.pkl")
            self.scaler_target = joblib.load(f"{model_path}_scaler_target.pkl")
            self.config = joblib.load(f"{model_path}_config.pkl")

            logger.info(f"Enhanced model loaded from {model_path}")
            return True
        except Exception as e:
            logger.error(f"Error loading enhanced model: {e}")
            return False

async def run_enhanced_reversion_analysis(
    stock_info_path: str = "data_store/stock_info.csv",
    symbols_path: str = "data_store/symbols_list.txt",
    duration_years: int = 5,
    max_pairs: int = 1000,
    save_model_path: str = None,
    max_stocks: int = 5000,
    strategy: str = "aggressive",
    extreme_level: str = "high",
    use_all_correlations: bool = True,
    min_correlation_level: str = "high"
):
    """
    运行增强的回归分析流程

    Parameters:
    -----------
    stock_info_path: str
        股票信息文件路径
    symbols_path: str
        股票代码文件路径
    duration_years: int
        数据年数
    max_pairs: int
        最大分析股票对数量
    save_model_path: str
        模型保存路径
    """
    logger.info("🚀 Starting Enhanced Reversion Time Prediction Analysis")
    logger.info("=" * 100)

    # 初始化增强预测器
    predictor = EnhancedReversionPredictor()

    # 加载股票信息
    logger.info("📊 Loading stock information...")
    stock_info = predictor.load_stock_info(stock_info_path)
    if not stock_info:
        logger.error("Failed to load stock information")
        return None

    # 获取股票代码
    logger.info("📋 Loading stock symbols...")
    symbols = predictor.get_stock_symbols(symbols_path)
    if not symbols:
        logger.error("Failed to load stock symbols")
        return None

    # 限制股票数量以控制计算时间
    if len(symbols) > max_stocks:
        symbols = symbols[:max_stocks]
        logger.info(f"⚠️ Limited to first {len(symbols)} stocks for analysis")

    # 下载历史数据
    logger.info(f"📈 Downloading {duration_years}-year historical data...")
    data = await predictor.download_historical_data(symbols, duration_years)
    if data is None:
        logger.error("Failed to download historical data")
        return None

    # 计算实际数据年数
    actual_data_years = calculate_actual_years_from_data(data)
    logger.info(f"📊 Actual data years based on K-bars: {actual_data_years} years")
    if actual_data_years != duration_years:
        logger.info(f"   Note: Requested {duration_years} years, but data contains {actual_data_years} years")

    # 计算相关性矩阵
    logger.info("🔗 Calculating correlation matrix...")
    correlation_matrix = predictor.calculate_correlation_matrix(data['price'])

    # 找到高相关性股票对
    logger.info("🎯 Finding high correlation pairs...")
    correlation_groups = predictor.find_high_correlation_pairs(data['tickers'], correlation_matrix)

    if not correlation_groups['all_pairs']:
        logger.error("No high correlation pairs found")
        return None

    # 智能选择股票对：优先选择高相关性，但不限制总数
    selected_pairs = []

    # 1. 优先选择极高相关性对（全部使用）
    selected_pairs.extend(correlation_groups['extreme_high'])

    # 2. 选择很高相关性对（限制数量以控制计算时间）
    very_high_limit = min(len(correlation_groups['very_high']), max_pairs // 4)
    selected_pairs.extend(correlation_groups['very_high'][:very_high_limit])

    # 3. 选择高相关性对（填充剩余配额）
    remaining_quota = max_pairs - len(selected_pairs)
    if remaining_quota > 0:
        high_limit = min(len(correlation_groups['high']), remaining_quota)
        selected_pairs.extend(correlation_groups['high'][:high_limit])

    # 4. 如果还有配额，选择中等相关性对
    remaining_quota = max_pairs - len(selected_pairs)
    if remaining_quota > 0:
        moderate_limit = min(len(correlation_groups['moderate']), remaining_quota)
        selected_pairs.extend(correlation_groups['moderate'][:moderate_limit])

    logger.info(f"📈 Selected {len(selected_pairs)} pairs for analysis:")
    logger.info(f"   🔥 Extreme High: {len(correlation_groups['extreme_high'])} pairs")
    logger.info(f"   🔴 Very High: {min(len(correlation_groups['very_high']), max_pairs // 4)} pairs")
    logger.info(f"   🟠 High: {len([p for p in selected_pairs if 0.7 < abs(p[2]) <= 0.85])} pairs")
    logger.info(f"   🟡 Moderate: {len([p for p in selected_pairs if abs(p[2]) <= 0.7])} pairs")

    high_corr_pairs = selected_pairs

    # 计算市场环境（使用第一只股票作为市场代理）
    logger.info("🌍 Detecting market environment...")
    market_environments = predictor.detect_market_environment(data['price'][0])

    # 检测背离事件和提取特征
    if use_all_correlations:
        logger.info("🚀 Using ALL high correlation stocks for comprehensive analysis...")

        # 使用所有高相关性股票进行分析
        analysis_results = predictor.analyze_all_high_correlation_stocks(
            data, correlation_groups, market_environments
        )

        all_events = analysis_results['events']
        all_features = analysis_results['features']
        correlation_stats = analysis_results['correlation_stats']

        logger.info(f"✅ Comprehensive analysis completed:")
        logger.info(f"   📊 Total events found: {len(all_events)}")
        logger.info(f"   🔥 From extreme high correlations: {correlation_stats['extreme_high_events']}")
        logger.info(f"   🔴 From very high correlations: {correlation_stats['very_high_events']}")
        logger.info(f"   🟠 From high correlations: {correlation_stats['high_events']}")
        logger.info(f"   🟡 From moderate correlations: {correlation_stats['moderate_events']}")

    else:
        logger.info("🔍 Detecting enhanced divergence events (limited pairs)...")
        all_events = []
        all_features = []

        for i, (ticker_a, ticker_b, correlation) in enumerate(high_corr_pairs):
            if i % 50 == 0:
                logger.info(f"Processing pair {i+1}/{len(high_corr_pairs)}: {ticker_a}-{ticker_b}")

            try:
                # 获取价格数据
                idx_a = data['tickers'].index(ticker_a)
                idx_b = data['tickers'].index(ticker_b)
                prices_a = data['price'][idx_a]
                prices_b = data['price'][idx_b]

                # 获取成交量数据（如果有）
                volumes_a = data.get('volume', [None] * len(data['tickers']))[idx_a]
                volumes_b = data.get('volume', [None] * len(data['tickers']))[idx_b]

                # 计算增强技术指标
                indicators_a = predictor.calculate_enhanced_technical_indicators(prices_a, volumes_a)
                indicators_b = predictor.calculate_enhanced_technical_indicators(prices_b, volumes_b)

                # 检测增强背离事件
                events = predictor.detect_enhanced_divergence_events(
                    prices_a, prices_b, indicators_a, indicators_b,
                    correlation, (ticker_a, ticker_b), market_environments
                )

                # 为每个事件提取增强特征
                for event in events:
                    features = predictor.extract_enhanced_features(event)
                    if features:
                        all_events.append(event)
                        all_features.append(features)

            except Exception as e:
                logger.warning(f"Error processing pair {ticker_a}-{ticker_b}: {e}")
                continue

        logger.info(f"✅ Found {len(all_events)} enhanced divergence events")

    # 统计分析
    completed_events = [e for e in all_events if e.reversion_days is not None]
    logger.info(f"📊 Completed reversions: {len(completed_events)}")

    if completed_events:
        reversion_days = [e.reversion_days for e in completed_events]
        logger.info(f"📈 Enhanced Reversion Statistics:")
        logger.info(f"   Mean: {np.mean(reversion_days):.1f} days")
        logger.info(f"   Median: {np.median(reversion_days):.1f} days")
        logger.info(f"   Range: {np.min(reversion_days):.0f} - {np.max(reversion_days):.0f} days")

        # 按行业板块统计
        same_sector_events = [e for e in completed_events if e.same_sector]
        cross_sector_events = [e for e in completed_events if not e.same_sector]

        if same_sector_events:
            same_sector_days = [e.reversion_days for e in same_sector_events]
            logger.info(f"   Same sector mean: {np.mean(same_sector_days):.1f} days ({len(same_sector_events)} events)")

        if cross_sector_events:
            cross_sector_days = [e.reversion_days for e in cross_sector_events]
            logger.info(f"   Cross sector mean: {np.mean(cross_sector_days):.1f} days ({len(cross_sector_events)} events)")

    # 保存结果
    results = {
        'events': all_events,
        'features': all_features,
        'predictor': predictor,
        'data': data,
        'correlation_matrix': correlation_matrix,
        'high_corr_pairs': high_corr_pairs,
        'market_environments': market_environments
    }

    # 如果有足够的数据，训练增强模型
    if len(completed_events) >= 20:
        logger.info("🤖 Training enhanced model...")

        # 准备模型数据
        time_series_data, static_data, targets = predictor.prepare_model_data(all_events, all_features)

        if time_series_data is not None and len(time_series_data) >= 20:
            # 训练模型
            training_results = predictor.train_enhanced_model(time_series_data, static_data, targets)

            if training_results:
                results['training_results'] = training_results
                logger.info(f"✅ Model training completed successfully!")
                logger.info(f"   Model type: {training_results['model_type']}")
                logger.info(f"   Validation MAE: {training_results['val_mae']:.2f} days")

                # 保存模型到新的缓存结构
                if save_model_path:
                    if save_model_path == 'enhanced_reversion_model':  # 默认路径
                        # 使用新的缓存结构和实际年数
                        predictor.save_enhanced_model(None, strategy, extreme_level, duration_years, actual_data_years)
                    else:
                        # 使用用户指定的路径和实际年数
                        predictor.save_enhanced_model(save_model_path, strategy, extreme_level, duration_years, actual_data_years)
            else:
                logger.error("Model training failed")
        else:
            logger.warning("Insufficient data for model training after preprocessing")
    else:
        logger.warning(f"⚠️ Only {len(completed_events)} completed events - need more data for reliable training")

    # 保存分析结果到results目录
    save_analysis_results(results, duration_years, strategy, extreme_level, actual_data_years)

    logger.info("🎉 Enhanced analysis completed!")
    return results

def calculate_actual_years_from_data(data: dict) -> int:
    """根据实际K线数量计算年数"""
    try:
        if 'dates' in data and len(data['dates']) > 0:
            # 如果有日期信息，直接计算
            dates = data['dates']
            if hasattr(dates, 'min') and hasattr(dates, 'max'):
                days = (dates.max() - dates.min()).days
                return max(1, round(days / 365.25))

        # 如果没有日期信息，根据K线数量估算
        if 'price' in data:
            if hasattr(data['price'], 'shape'):
                bars_count = data['price'].shape[1]
            elif isinstance(data['price'], list) and len(data['price']) > 0:
                bars_count = len(data['price'][0]) if data['price'][0] else 0
            else:
                bars_count = 0

            # 按252个交易日/年计算
            if bars_count > 0:
                trading_years = bars_count / 252
                return max(1, round(trading_years))

        return 3  # 默认值
    except Exception as e:
        logger.warning(f"Failed to calculate actual years: {e}")
        return 3

def save_analysis_results(results: dict, duration_years: int, strategy: str, extreme_level: str, actual_data_years: int = None):
    """保存分析结果到results目录"""
    try:
        from datetime import datetime
        import json

        # 使用实际数据年数而不是参数年数
        if actual_data_years is None:
            actual_data_years = duration_years

        # 创建results目录结构
        date_str = datetime.now().strftime("%Y%m%d")
        results_dir = f"results/{date_str}"
        os.makedirs(results_dir, exist_ok=True)

        # 生成结果文件名（使用实际年数）
        timestamp = datetime.now().strftime("%H%M%S")
        filename_base = f"enhanced_reversion_{strategy}_{extreme_level}_{actual_data_years}y_{timestamp}"

        # 保存详细结果（JSON格式）
        json_path = os.path.join(results_dir, f"{filename_base}.json")

        # 准备可序列化的结果数据
        serializable_results = {}
        for key, value in results.items():
            if key == 'training_results' and value:
                # 处理训练结果
                training_data = {}
                for k, v in value.items():
                    if isinstance(v, (int, float, str, bool, list)):
                        training_data[k] = v
                    elif hasattr(v, 'tolist'):  # numpy arrays
                        training_data[k] = v.tolist()
                    else:
                        training_data[k] = str(v)
                serializable_results[key] = training_data
            elif key == 'events':
                # 处理事件列表 - 转换为字典格式
                events_data = []
                for event in value:
                    if hasattr(event, '__dict__'):
                        event_dict = {}
                        for attr, attr_value in event.__dict__.items():
                            if isinstance(attr_value, (int, float, str, bool, type(None))):
                                event_dict[attr] = attr_value
                            elif hasattr(attr_value, 'tolist'):
                                event_dict[attr] = attr_value.tolist()
                            else:
                                event_dict[attr] = str(attr_value)
                        events_data.append(event_dict)
                    else:
                        events_data.append(str(event))
                serializable_results[key] = events_data
            elif isinstance(value, (int, float, str, bool, list, dict)):
                serializable_results[key] = value
            elif hasattr(value, 'tolist'):  # numpy arrays
                serializable_results[key] = value.tolist()
            else:
                serializable_results[key] = str(value)

        # 添加元数据
        serializable_results['metadata'] = {
            'analysis_date': datetime.now().isoformat(),
            'duration_years': duration_years,
            'strategy': strategy,
            'extreme_level': extreme_level,
            'version': '1.0'
        }

        with open(json_path, 'w', encoding='utf-8') as f:
            json.dump(serializable_results, f, indent=2, ensure_ascii=False)

        logger.info(f"📊 Analysis results saved to: {json_path}")

        # 保存简化的CSV报告
        csv_path = os.path.join(results_dir, f"{filename_base}_summary.csv")
        save_csv_summary(results, csv_path)

        logger.info(f"📈 Summary report saved to: {csv_path}")

    except Exception as e:
        logger.error(f"Failed to save analysis results: {e}")

def save_csv_summary(results: dict, csv_path: str):
    """保存CSV格式的摘要报告"""
    try:
        import pandas as pd

        summary_data = []

        # 基本信息
        summary_data.append({
            'Metric': 'Total Events',
            'Value': len(results.get('events', [])),
            'Description': 'Total divergence events detected'
        })

        summary_data.append({
            'Metric': 'Completed Events',
            'Value': len([e for e in results.get('events', []) if e.reversion_days is not None]),
            'Description': 'Events with completed reversion'
        })

        # 训练结果
        if 'training_results' in results and results['training_results']:
            tr = results['training_results']
            summary_data.append({
                'Metric': 'Model Type',
                'Value': tr.get('model_type', 'Unknown'),
                'Description': 'Type of model used'
            })

            summary_data.append({
                'Metric': 'Validation MAE',
                'Value': f"{tr.get('val_mae', 0):.2f} days",
                'Description': 'Mean Absolute Error on validation set'
            })

            summary_data.append({
                'Metric': 'Training MAE',
                'Value': f"{tr.get('train_mae', 0):.2f} days",
                'Description': 'Mean Absolute Error on training set'
            })

        # 相关性统计
        if 'correlation_stats' in results:
            cs = results['correlation_stats']
            summary_data.append({
                'Metric': 'High Correlation Pairs',
                'Value': cs.get('high_correlation_pairs', 0),
                'Description': 'Number of highly correlated stock pairs'
            })

        # 创建DataFrame并保存
        df = pd.DataFrame(summary_data)
        df.to_csv(csv_path, index=False, encoding='utf-8')

    except Exception as e:
        logger.error(f"Failed to save CSV summary: {e}")

if __name__ == "__main__":
    import argparse
    import asyncio
    from config import get_model_config, get_balanced_config, get_conservative_config, get_aggressive_config

    # 命令行参数解析
    parser = argparse.ArgumentParser(description='Enhanced Reversion Time Prediction Analysis')

    # 数据配置
    parser.add_argument('--duration-years', type=int, default=5,
                       help='历史数据年数 (默认: 5)')
    parser.add_argument('--max-pairs', type=int, default=1000,
                       help='最大分析股票对数量 (默认: 1000)')
    parser.add_argument('--max-stocks', type=int, default=5000,
                       help='最大股票数量 (默认: 5000)')

    # 模型配置
    parser.add_argument('--model-type', choices=['lstm', 'cnn_gru', 'ensemble'],
                       default='cnn_gru', help='模型类型 (默认: cnn_gru)')
    parser.add_argument('--enable-ensemble', action='store_true',
                       help='启用集成预测 (LSTM + CNN-GRU平均)')
    parser.add_argument('--ensemble-weights', nargs=2, type=float,
                       default=[0.4, 0.6], metavar=('LSTM_WEIGHT', 'CNN_GRU_WEIGHT'),
                       help='集成权重 (默认: 0.4 0.6)')

    # 策略配置
    parser.add_argument('--strategy', choices=['conservative', 'balanced', 'aggressive'],
                       default='aggressive', help='策略类型 (默认: aggressive)')
    parser.add_argument('--extreme-level', choices=['high', 'medium', 'low'],
                       default='high', help='极端级别 (默认: high)')

    # 训练配置
    parser.add_argument('--epochs', type=int, default=200,
                       help='训练轮数 (默认: 200)')
    parser.add_argument('--batch-size', type=int, default=64,
                       help='批次大小 (默认: 64)')
    parser.add_argument('--learning-rate', type=float, default=0.0005,
                       help='学习率 (默认: 0.0005)')

    # 输出配置
    parser.add_argument('--save-model', type=str, default='enhanced_reversion_model',
                       help='模型保存路径 (默认: enhanced_reversion_model)')
    parser.add_argument('--no-save', action='store_true',
                       help='不保存模型')

    # 其他配置
    parser.add_argument('--correlation-threshold', type=float, default=0.6,
                       help='相关性阈值 (默认: 0.6)')
    parser.add_argument('--divergence-threshold', type=float, default=1.5,
                       help='背离阈值 (默认: 1.5)')
    parser.add_argument('--use-all-correlations', action='store_true', default=True,
                       help='使用所有高相关性股票对进行训练（默认: True）')
    parser.add_argument('--min-correlation-level', choices=['moderate', 'high', 'very_high', 'extreme_high'],
                       default='high', help='最低相关性级别 (默认: high)')

    args = parser.parse_args()

    # 显示配置信息
    print("🚀 Enhanced Reversion Time Prediction Analysis")
    print("=" * 80)
    print(f"📊 数据配置:")
    print(f"   历史数据年数: {args.duration_years} 年")
    print(f"   最大股票数量: {args.max_stocks}")
    print(f"   最大分析股票对: {args.max_pairs}")
    print()
    print(f"🧠 模型配置:")
    print(f"   模型类型: {args.model_type}")
    print(f"   集成预测: {'是' if args.enable_ensemble else '否'}")
    if args.enable_ensemble:
        print(f"   集成权重: LSTM={args.ensemble_weights[0]}, CNN-GRU={args.ensemble_weights[1]}")
    print(f"   训练轮数: {args.epochs}")
    print(f"   批次大小: {args.batch_size}")
    print(f"   学习率: {args.learning_rate}")
    print()
    print(f"📈 策略配置:")
    print(f"   策略类型: {args.strategy}")
    print(f"   极端级别: {args.extreme_level}")
    print(f"   相关性阈值: {args.correlation_threshold}")
    print(f"   背离阈值: {args.divergence_threshold}")
    print()

    # 自动启用集成预测如果模型类型是ensemble
    if args.model_type == 'ensemble':
        args.enable_ensemble = True

    # 获取配置
    if args.strategy == 'conservative':
        config_dict = get_conservative_config()
    elif args.strategy == 'aggressive':
        config_dict = get_aggressive_config()
    else:
        config_dict = get_balanced_config()

    # 更新模型配置
    model_config = get_model_config(args.model_type, args.enable_ensemble)
    model_config.epochs = args.epochs
    model_config.batch_size = args.batch_size
    model_config.learning_rate = args.learning_rate
    model_config.correlation_threshold = args.correlation_threshold
    model_config.divergence_threshold = args.divergence_threshold
    if args.enable_ensemble:
        model_config.ensemble_weights = args.ensemble_weights

    # 创建增强预测器并设置配置
    predictor = EnhancedReversionPredictor()
    predictor.config.update({
        'model_type': args.model_type,
        'enable_ensemble': args.enable_ensemble,
        'ensemble_weights': args.ensemble_weights,
        'epochs': args.epochs,
        'batch_size': args.batch_size,
        'learning_rate': args.learning_rate,
        'correlation_threshold': args.correlation_threshold,
        'divergence_threshold': args.divergence_threshold,
    })

    # 运行增强分析
    results = asyncio.run(run_enhanced_reversion_analysis(
        duration_years=args.duration_years,
        max_pairs=args.max_pairs,
        save_model_path=None if args.no_save else args.save_model,
        max_stocks=args.max_stocks,
        strategy=args.strategy,
        extreme_level=args.extreme_level,
        use_all_correlations=args.use_all_correlations,
        min_correlation_level=args.min_correlation_level
    ))
