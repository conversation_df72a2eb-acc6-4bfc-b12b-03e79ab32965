2025-06-14 09:06:30,327 - __main__ - INFO - 📝 Logging configured: results/20250614/logs/ibkr_trading.log
2025-06-14 09:06:30,327 - __main__ - INFO - 🚀 Starting daily trading analysis workflow...
2025-06-14 09:06:30,327 - __main__ - INFO - 🔧 System configuration: enable_options=True
2025-06-14 09:06:30,327 - __main__ - INFO - ============================================================
2025-06-14 09:06:30,327 - __main__ - INFO - Skipping stock universe update (use --update-universe to enable)
2025-06-14 09:06:30,327 - __main__ - INFO - Step 1: Downloading data for all stocks...
2025-06-14 09:06:30,328 - __main__ - INFO - Found 5915 symbols to analyze
2025-06-14 09:06:30,328 - __main__ - INFO - Downloading data for ALL 5915 symbols to maximize correlation opportunities
2025-06-14 09:06:30,328 - __main__ - INFO - Downloading data from 2024-06-14 to 2025-06-14
2025-06-14 09:06:30,328 - ib_insync.client - INFO - Connecting to 127.0.0.1:7497 with clientId 2234...
2025-06-14 09:06:30,328 - ib_insync.client - INFO - Disconnecting
2025-06-14 09:06:30,328 - ib_insync.client - ERROR - API connection failed: ConnectionRefusedError(61, "Connect call failed ('127.0.0.1', 7497)")
2025-06-14 09:06:30,328 - ib_insync.client - ERROR - Make sure API port on TWS/IBG is open
2025-06-14 09:06:30,328 - IBKRClient - ERROR - Failed to connect to IBKR: [Errno 61] Connect call failed ('127.0.0.1', 7497)
2025-06-14 09:06:30,328 - ibkr_data_provider - INFO - Connected to IBKR for sequential downloading (optimized for data farm stability)
2025-06-14 09:06:30,329 - ibkr_data_provider - INFO - 🚀 Starting optimized concurrent download
2025-06-14 09:06:30,329 - ibkr_data_provider - INFO - 📊 Total stocks: 5915 (cached: 0, downloading: 5915)
2025-06-14 09:06:30,329 - ibkr_data_provider - INFO - ⚡ Adaptive concurrent limit: 1
2025-06-14 09:06:30,329 - ibkr_data_provider - INFO - 📅 Date range: 2024-06-14 to 2025-06-14
2025-06-14 09:06:30,330 - ibkr_data_provider - INFO - 📂 Progress loaded: 5539 completed, 376 failed, 0 invalid
2025-06-14 09:06:30,330 - ibkr_data_provider - INFO - 🔄 Resuming download from symbol 5915/5915
2025-06-14 09:06:30,330 - ibkr_data_provider - INFO - 📊 Previous progress: 5539 completed, 376 failed
2025-06-14 09:06:30,330 - ibkr_data_provider - INFO - ✅ All data available from previous downloads, loading from incremental data...
2025-06-14 09:06:30,330 - ibkr_data_provider - INFO - ⚠️  No incremental data found, trying to load from legacy data.pickle...
2025-06-14 09:06:30,330 - ibkr_data_provider - INFO - 📦 No data.pickle file found
2025-06-14 09:06:30,330 - ibkr_data_provider - WARNING - ⚠️  No legacy data found either, will proceed with empty cache
2025-06-14 09:06:30,330 - ibkr_data_provider - INFO - ✅ All data available from cache!
2025-06-14 09:06:30,330 - ibkr_data_provider - INFO - 🎯 Sequential download completed!
2025-06-14 09:06:30,330 - ibkr_data_provider - INFO - ✅ Successful: 5539
2025-06-14 09:06:30,330 - ibkr_data_provider - INFO - ❌ Failed: 376
2025-06-14 09:06:30,330 - ibkr_data_provider - INFO - 🚫 Invalid symbols: 0
2025-06-14 09:06:30,330 - ibkr_data_provider - INFO - 📈 Success rate: 93.6%
2025-06-14 09:06:30,330 - ibkr_data_provider - INFO - ⚡ Download rate: 3541501.5 stocks/second
2025-06-14 09:06:30,331 - ibkr_data_provider - INFO - ⏱️  Total time: 0.0 seconds
2025-06-14 09:06:30,331 - ibkr_data_provider - INFO - 📂 Resume capability: Progress saved for 5539 symbols
2025-06-14 09:06:30,331 - ibkr_data_provider - INFO - 💾 Progress saved - can resume with 376 failed symbols
2025-06-14 09:06:30,331 - __main__ - INFO - ✅ IBKR connection transferred to main client - keeping alive for trading
2025-06-14 09:06:30,331 - __main__ - ERROR - Failed to download stock data
2025-06-14 09:06:30,331 - __main__ - ERROR - Step 1 failed - aborting workflow
2025-06-14 09:06:55,689 - __main__ - INFO - 📝 Logging configured: results/20250614/logs/ibkr_trading.log
2025-06-14 09:06:55,689 - __main__ - INFO - 🚀 Starting daily trading analysis workflow...
2025-06-14 09:06:55,689 - __main__ - INFO - 🔧 System configuration: enable_options=True
2025-06-14 09:06:55,689 - __main__ - INFO - ============================================================
2025-06-14 09:06:55,689 - __main__ - INFO - Skipping stock universe update (use --update-universe to enable)
2025-06-14 09:06:55,689 - __main__ - INFO - Step 1: Downloading data for all stocks...
2025-06-14 09:06:55,689 - __main__ - INFO - Found 5915 symbols to analyze
2025-06-14 09:06:55,689 - __main__ - INFO - Downloading data for ALL 5915 symbols to maximize correlation opportunities
2025-06-14 09:06:55,689 - __main__ - INFO - Downloading data from 2024-06-14 to 2025-06-14
2025-06-14 09:06:55,689 - ib_insync.client - INFO - Connecting to 127.0.0.1:7497 with clientId 9022...
2025-06-14 09:06:55,690 - ib_insync.client - INFO - Connected
2025-06-14 09:06:55,742 - ib_insync.client - INFO - Logged on to server version 176
2025-06-14 09:06:55,768 - ib_insync.client - INFO - API connection ready
2025-06-14 09:06:55,774 - ib_insync.wrapper - INFO - Warning 2104, reqId -1: \u5e02\u573a\u6570\u636e\u519c\u573a\u8fde\u63a5\u6b63\u5e38:hfarm
2025-06-14 09:06:55,774 - ib_insync.wrapper - INFO - Warning 2106, reqId -1: \u5386\u53f2\u5e02\u573a\u6570\u636e\u519c\u573a\u8fde\u63a5\u6b63\u5e38:apachmds
2025-06-14 09:06:55,774 - ib_insync.wrapper - INFO - Warning 2158, reqId -1: Sec-def\u6570\u636e\u519c\u573a\u8fde\u63a5\u6b63\u5e38:secdefhk
2025-06-14 09:06:55,853 - ib_insync.wrapper - INFO - position: Position(account='DUK362830', contract=Stock(conId=*********, symbol='EICB', exchange='NYSE', currency='USD', localSymbol='EICB', tradingClass='EICB'), position=134.0, avgCost=24.********)
2025-06-14 09:06:55,853 - ib_insync.wrapper - INFO - position: Position(account='DUK362830', contract=Stock(conId=*********, symbol='RCC', exchange='NYSE', currency='USD', localSymbol='RCC', tradingClass='RCC'), position=136.0, avgCost=24.********)
2025-06-14 09:06:55,853 - ib_insync.wrapper - INFO - position: Position(account='DUK362830', contract=Stock(conId=*********, symbol='FSHP', exchange='ISLAND', currency='USD', localSymbol='FSHP', tradingClass='NMS'), position=319.0, avgCost=10.5450351)
2025-06-14 09:06:55,853 - ib_insync.wrapper - INFO - position: Position(account='DUK362830', contract=Stock(conId=********, symbol='GJS', exchange='NYSE', currency='USD', localSymbol='GJS', tradingClass='GJS'), position=148.0, avgCost=22.9167919)
2025-06-14 09:06:55,853 - ib_insync.wrapper - INFO - position: Position(account='DUK362830', contract=Stock(conId=*********, symbol='WTFCP', exchange='ISLAND', currency='USD', localSymbol='WTFCP', tradingClass='NMS'), position=0.0, avgCost=0.0)
2025-06-14 09:06:55,853 - ib_insync.wrapper - INFO - position: Position(account='DUK362830', contract=Stock(conId=272093, symbol='MSFT', exchange='ISLAND', currency='USD', localSymbol='MSFT', tradingClass='NMS'), position=7.0, avgCost=472.9429)
2025-06-14 09:06:55,853 - ib_insync.wrapper - INFO - position: Position(account='DUK362830', contract=Stock(conId=*********, symbol='HROWL', exchange='ISLAND', currency='USD', localSymbol='HROWL', tradingClass='NMS'), position=132.0, avgCost=25.2876106)
2025-06-14 09:06:55,853 - ib_insync.wrapper - INFO - position: Position(account='DUK362830', contract=Stock(conId=*********, symbol='SSSSL', exchange='ISLAND', currency='USD', localSymbol='SSSSL', tradingClass='NMS'), position=0.0, avgCost=0.0)
2025-06-14 09:06:55,853 - ib_insync.wrapper - INFO - position: Position(account='DUK362830', contract=Stock(conId=********, symbol='GJT', exchange='NYSE', currency='USD', localSymbol='GJT', tradingClass='GJT'), position=147.0, avgCost=22.8068374)
2025-06-14 09:06:55,853 - ib_insync.wrapper - INFO - position: Position(account='DUK362830', contract=Stock(conId=*********, symbol='RWAYL', exchange='ISLAND', currency='USD', localSymbol='RWAYL', tradingClass='NMS'), position=133.0, avgCost=25.********)
2025-06-14 09:06:55,853 - ib_insync.wrapper - INFO - position: Position(account='DUK362830', contract=Stock(conId=10754, symbol='OHI', exchange='NYSE', currency='USD', localSymbol='OHI', tradingClass='OHI'), position=180.0, avgCost=36.9761464)
2025-06-14 09:06:55,853 - ib_insync.wrapper - INFO - position: Position(account='DUK362830', contract=Stock(conId=********, symbol='ALE', exchange='NYSE', currency='USD', localSymbol='ALE', tradingClass='ALE'), position=51.0, avgCost=65.********)
2025-06-14 09:06:55,853 - ib_insync.wrapper - INFO - position: Position(account='DUK362830', contract=Stock(conId=*********, symbol='GPAT', exchange='ISLAND', currency='USD', localSymbol='GPAT', tradingClass='NMS'), position=314.0, avgCost=10.655035)
2025-06-14 09:06:55,853 - ib_insync.wrapper - INFO - position: Position(account='DUK362830', contract=Stock(conId=*********, symbol='MITN', exchange='NYSE', currency='USD', localSymbol='MITN', tradingClass='MITN'), position=399.0, avgCost=25.********)
2025-06-14 09:06:55,853 - ib_insync.wrapper - INFO - position: Position(account='DUK362830', contract=Stock(conId=*********, symbol='TECTP', exchange='ISLAND', currency='USD', localSymbol='TECTP', tradingClass='SCM'), position=0.0, avgCost=0.0)
2025-06-14 09:06:55,853 - ib_insync.wrapper - INFO - position: Position(account='DUK362830', contract=Stock(conId=*********, symbol='SAZ', exchange='NYSE', currency='USD', localSymbol='SAZ', tradingClass='SAZ'), position=264.0, avgCost=25.2476107)
2025-06-14 09:06:56,154 - ib_insync.wrapper - INFO - updatePortfolio: PortfolioItem(contract=Stock(conId=********, symbol='ALE', right='0', primaryExchange='NYSE', currency='USD', localSymbol='ALE', tradingClass='ALE'), position=51.0, marketPrice=64.********, marketValue=3304.23, averageCost=65.********, unrealizedPNL=-33.2, realizedPNL=0.0, account='DUK362830')
2025-06-14 09:06:56,154 - ib_insync.wrapper - INFO - updatePortfolio: PortfolioItem(contract=Stock(conId=*********, symbol='EICB', right='0', primaryExchange='NYSE', currency='USD', localSymbol='EICB', tradingClass='EICB'), position=134.0, marketPrice=24.********, marketValue=3332.47, averageCost=24.********, unrealizedPNL=-3.8, realizedPNL=0.0, account='DUK362830')
2025-06-14 09:06:56,154 - ib_insync.wrapper - INFO - updatePortfolio: PortfolioItem(contract=Stock(conId=*********, symbol='FSHP', right='0', primaryExchange='ISLAND', currency='USD', localSymbol='FSHP', tradingClass='NMS'), position=319.0, marketPrice=10.********, marketValue=3339.79, averageCost=10.5450351, unrealizedPNL=-24.07, realizedPNL=0.0, account='DUK362830')
2025-06-14 09:06:56,155 - ib_insync.wrapper - INFO - updatePortfolio: PortfolioItem(contract=Stock(conId=********, symbol='GJS', right='0', primaryExchange='NYSE', currency='USD', localSymbol='GJS', tradingClass='GJS'), position=148.0, marketPrice=22.********, marketValue=3359.6, averageCost=22.9167919, unrealizedPNL=-32.09, realizedPNL=0.0, account='DUK362830')
2025-06-14 09:06:56,155 - ib_insync.wrapper - INFO - updatePortfolio: PortfolioItem(contract=Stock(conId=********, symbol='GJT', right='0', primaryExchange='NYSE', currency='USD', localSymbol='GJT', tradingClass='GJT'), position=147.0, marketPrice=22.********, marketValue=3321.96, averageCost=22.8068374, unrealizedPNL=-30.65, realizedPNL=0.0, account='DUK362830')
2025-06-14 09:06:56,155 - ib_insync.wrapper - INFO - updatePortfolio: PortfolioItem(contract=Stock(conId=*********, symbol='GPAT', right='0', primaryExchange='ISLAND', currency='USD', localSymbol='GPAT', tradingClass='NMS'), position=314.0, marketPrice=10.********, marketValue=3326.96, averageCost=10.655035, unrealizedPNL=-18.72, realizedPNL=0.0, account='DUK362830')
2025-06-14 09:06:56,155 - ib_insync.wrapper - INFO - updatePortfolio: PortfolioItem(contract=Stock(conId=*********, symbol='HROWL', right='0', primaryExchange='ISLAND', currency='USD', localSymbol='HROWL', tradingClass='NMS'), position=132.0, marketPrice=25.********, marketValue=3344.52, averageCost=25.2876106, unrealizedPNL=6.56, realizedPNL=0.0, account='DUK362830')
2025-06-14 09:06:56,155 - ib_insync.wrapper - INFO - updatePortfolio: PortfolioItem(contract=Stock(conId=*********, symbol='MITN', right='0', primaryExchange='NYSE', currency='USD', localSymbol='MITN', tradingClass='MITN'), position=399.0, marketPrice=25.********, marketValue=9988.37, averageCost=25.********, unrealizedPNL=-0.29, realizedPNL=0.0, account='DUK362830')
2025-06-14 09:06:56,155 - ib_insync.wrapper - INFO - updatePortfolio: PortfolioItem(contract=Stock(conId=272093, symbol='MSFT', right='0', primaryExchange='ISLAND', currency='USD', localSymbol='MSFT', tradingClass='NMS'), position=7.0, marketPrice=474.********, marketValue=3320.73, averageCost=472.9429, unrealizedPNL=10.13, realizedPNL=0.0, account='DUK362830')
2025-06-14 09:06:56,155 - ib_insync.wrapper - INFO - updatePortfolio: PortfolioItem(contract=Stock(conId=10754, symbol='OHI', right='0', primaryExchange='NYSE', currency='USD', localSymbol='OHI', tradingClass='OHI'), position=180.0, marketPrice=37.0288925, marketValue=6665.2, averageCost=36.9761464, unrealizedPNL=9.49, realizedPNL=0.0, account='DUK362830')
2025-06-14 09:06:56,156 - ib_insync.wrapper - INFO - updatePortfolio: PortfolioItem(contract=Stock(conId=*********, symbol='RCC', right='0', primaryExchange='NYSE', currency='USD', localSymbol='RCC', tradingClass='RCC'), position=136.0, marketPrice=24.445488, marketValue=3324.59, averageCost=24.********, unrealizedPNL=-4.34, realizedPNL=0.0, account='DUK362830')
2025-06-14 09:06:56,156 - ib_insync.wrapper - INFO - updatePortfolio: PortfolioItem(contract=Stock(conId=*********, symbol='RWAYL', right='0', primaryExchange='ISLAND', currency='USD', localSymbol='RWAYL', tradingClass='NMS'), position=133.0, marketPrice=25.0811329, marketValue=3335.79, averageCost=25.********, unrealizedPNL=-4.84, realizedPNL=0.0, account='DUK362830')
2025-06-14 09:06:56,156 - ib_insync.wrapper - INFO - updatePortfolio: PortfolioItem(contract=Stock(conId=*********, symbol='SAZ', right='0', primaryExchange='NYSE', currency='USD', localSymbol='SAZ', tradingClass='SAZ'), position=264.0, marketPrice=25.********, marketValue=6655.47, averageCost=25.2476107, unrealizedPNL=-9.9, realizedPNL=0.0, account='DUK362830')
2025-06-14 09:06:56,156 - ib_insync.wrapper - INFO - updatePortfolio: PortfolioItem(contract=Stock(conId=*********, symbol='SSSSL', right='0', primaryExchange='ISLAND', currency='USD', localSymbol='SSSSL', tradingClass='NMS'), position=0.0, marketPrice=24.5400181, marketValue=0.0, averageCost=0.0, unrealizedPNL=0.0, realizedPNL=-118.64, account='DUK362830')
2025-06-14 09:06:56,156 - ib_insync.wrapper - INFO - updatePortfolio: PortfolioItem(contract=Stock(conId=*********, symbol='TECTP', right='0', primaryExchange='ISLAND', currency='USD', localSymbol='TECTP', tradingClass='SCM'), position=0.0, marketPrice=10.5975952, marketValue=0.0, averageCost=0.0, unrealizedPNL=0.0, realizedPNL=-34.83, account='DUK362830')
2025-06-14 09:06:56,157 - ib_insync.wrapper - INFO - updatePortfolio: PortfolioItem(contract=Stock(conId=*********, symbol='WTFCP', right='0', primaryExchange='ISLAND', currency='USD', localSymbol='WTFCP', tradingClass='NMS'), position=0.0, marketPrice=25.3009777, marketValue=0.0, averageCost=0.0, unrealizedPNL=0.0, realizedPNL=-1.03, account='DUK362830')
2025-06-14 09:06:56,265 - ib_insync.wrapper - INFO - execDetails Execution(execId='00025b47.684bba2c.01.01', time=datetime.datetime(2025, 6, 13, 18, 42, 49, tzinfo=datetime.timezone.utc), acctNumber='DUK362830', exchange='SMART', side='SLD', shares=131.0, price=25.29, permId=*********, clientId=0, orderId=0, liquidation=0, cumQty=131.0, avgPrice=25.29, orderRef='', evRule='', evMultiplier=0.0, modelCode='', lastLiquidity=2)
2025-06-14 09:06:56,269 - ib_insync.wrapper - INFO - execDetails Execution(execId='00025b46.684c65e7.01.01', time=datetime.datetime(2025, 6, 13, 18, 43, 22, tzinfo=datetime.timezone.utc), acctNumber='DUK362830', exchange='DRCTEDGE', side='SLD', shares=316.0, price=10.55, permId=*********, clientId=0, orderId=0, liquidation=0, cumQty=316.0, avgPrice=10.55, orderRef='', evRule='', evMultiplier=0.0, modelCode='', lastLiquidity=2)
2025-06-14 09:06:56,269 - ib_insync.wrapper - INFO - execDetails Execution(execId='00025b49.684d832a.01.01', time=datetime.datetime(2025, 6, 13, 18, 44, 4, tzinfo=datetime.timezone.utc), acctNumber='DUK362830', exchange='SMART', side='SLD', shares=200.0, price=24.42, permId=210530219, clientId=0, orderId=0, liquidation=0, cumQty=200.0, avgPrice=24.42, orderRef='', evRule='', evMultiplier=0.0, modelCode='', lastLiquidity=2)
2025-06-14 09:06:56,270 - ib_insync.wrapper - INFO - execDetails Execution(execId='00025b49.684daa37.01.01', time=datetime.datetime(2025, 6, 13, 20, 0, tzinfo=datetime.timezone.utc), acctNumber='DUK362830', exchange='SMART', side='SLD', shares=68.0, price=24.42, permId=210530219, clientId=0, orderId=0, liquidation=0, cumQty=268.0, avgPrice=24.42, orderRef='', evRule='', evMultiplier=0.0, modelCode='', lastLiquidity=2)
2025-06-14 09:06:56,270 - ib_insync.wrapper - INFO - commissionReport: CommissionReport(execId='00025b47.684bba2c.01.01', commission=1.026331, currency='USD', realizedPNL=-1.030916, yield_=0.0, yieldRedemptionDate=0)
2025-06-14 09:06:56,270 - ib_insync.wrapper - INFO - commissionReport: CommissionReport(execId='00025b46.684c65e7.01.01', commission=1.643516, currency='USD', realizedPNL=-34.834616, yield_=0.0, yieldRedemptionDate=0)
2025-06-14 09:06:56,270 - ib_insync.wrapper - INFO - commissionReport: CommissionReport(execId='00025b49.684d832a.01.01', commission=1.0402, currency='USD', realizedPNL=-88.539752, yield_=0.0, yieldRedemptionDate=0)
2025-06-14 09:06:56,270 - ib_insync.wrapper - INFO - commissionReport: CommissionReport(execId='00025b49.684daa37.01.01', commission=0.353668, currency='USD', realizedPNL=-30.103516, yield_=0.0, yieldRedemptionDate=0)
2025-06-14 09:06:56,270 - ib_insync.ib - INFO - Synchronization complete
2025-06-14 09:06:56,271 - IBKRClient - INFO - Connected to IBKR at 127.0.0.1:7497
2025-06-14 09:06:56,316 - IBKRClient - INFO - Account type: INDIVIDUAL
2025-06-14 09:06:56,317 - ibkr_data_provider - INFO - Connected to IBKR for sequential downloading (optimized for data farm stability)
2025-06-14 09:06:56,319 - ibkr_data_provider - INFO - 🚀 Starting optimized concurrent download
2025-06-14 09:06:56,319 - ibkr_data_provider - INFO - 📊 Total stocks: 5915 (cached: 0, downloading: 5915)
2025-06-14 09:06:56,319 - ibkr_data_provider - INFO - ⚡ Adaptive concurrent limit: 1
2025-06-14 09:06:56,319 - ibkr_data_provider - INFO - 📅 Date range: 2024-06-14 to 2025-06-14
2025-06-14 09:06:56,324 - ibkr_data_provider - INFO - 📂 Progress loaded: 5539 completed, 376 failed, 0 invalid
2025-06-14 09:06:56,325 - ibkr_data_provider - INFO - 🔄 Resuming download from symbol 5915/5915
2025-06-14 09:06:56,325 - ibkr_data_provider - INFO - 📊 Previous progress: 5539 completed, 376 failed
2025-06-14 09:06:56,326 - ibkr_data_provider - INFO - ✅ All data available from previous downloads, loading from incremental data...
2025-06-14 09:06:56,326 - ibkr_data_provider - INFO - ⚠️  No incremental data found, trying to load from legacy data.pickle...
2025-06-14 09:06:56,326 - ibkr_data_provider - INFO - 📦 No data.pickle file found
2025-06-14 09:06:56,327 - ibkr_data_provider - WARNING - ⚠️  No legacy data found either, will proceed with empty cache
2025-06-14 09:06:56,327 - ibkr_data_provider - INFO - ✅ All data available from cache!
2025-06-14 09:06:56,327 - ibkr_data_provider - INFO - 🎯 Sequential download completed!
2025-06-14 09:06:56,327 - ibkr_data_provider - INFO - ✅ Successful: 5539
2025-06-14 09:06:56,327 - ibkr_data_provider - INFO - ❌ Failed: 376
2025-06-14 09:06:56,327 - ibkr_data_provider - INFO - 🚫 Invalid symbols: 0
2025-06-14 09:06:56,327 - ibkr_data_provider - INFO - 📈 Success rate: 93.6%
2025-06-14 09:06:56,327 - ibkr_data_provider - INFO - ⚡ Download rate: 771989.4 stocks/second
2025-06-14 09:06:56,327 - ibkr_data_provider - INFO - ⏱️  Total time: 0.0 seconds
2025-06-14 09:06:56,327 - ibkr_data_provider - INFO - 📂 Resume capability: Progress saved for 5539 symbols
2025-06-14 09:06:56,327 - ibkr_data_provider - INFO - 💾 Progress saved - can resume with 376 failed symbols
2025-06-14 09:06:56,327 - __main__ - INFO - ✅ IBKR connection transferred to main client - keeping alive for trading
2025-06-14 09:06:56,327 - __main__ - ERROR - Failed to download stock data
2025-06-14 09:06:56,328 - __main__ - ERROR - Step 1 failed - aborting workflow
2025-06-14 09:06:56,396 - ib_insync.ib - INFO - Disconnecting from 127.0.0.1:7497, 713 B sent in 9 messages, 35.3 kB received in 700 messages, session time 706 ms.
2025-06-14 09:06:56,396 - ib_insync.client - INFO - Disconnecting
2025-06-14 09:07:15,126 - __main__ - INFO - 📝 Logging configured: results/20250614/logs/ibkr_trading.log
2025-06-14 09:07:15,126 - __main__ - INFO - 🚀 Starting daily trading analysis workflow...
2025-06-14 09:07:15,127 - __main__ - INFO - 🔧 System configuration: enable_options=True
2025-06-14 09:07:15,127 - __main__ - INFO - ============================================================
2025-06-14 09:07:15,127 - __main__ - INFO - Skipping stock universe update (use --update-universe to enable)
2025-06-14 09:07:15,127 - __main__ - INFO - Step 1: Downloading data for all stocks...
2025-06-14 09:07:15,127 - __main__ - INFO - Found 5915 symbols to analyze
2025-06-14 09:07:15,127 - __main__ - INFO - Downloading data for ALL 5915 symbols to maximize correlation opportunities
2025-06-14 09:07:15,127 - __main__ - INFO - Downloading data from 2024-06-14 to 2025-06-14
2025-06-14 09:07:15,127 - ib_insync.client - INFO - Connecting to 127.0.0.1:7497 with clientId 8719...
2025-06-14 09:07:15,127 - ib_insync.client - INFO - Connected
2025-06-14 09:07:15,142 - ib_insync.client - INFO - Logged on to server version 176
2025-06-14 09:07:15,153 - ib_insync.client - INFO - API connection ready
2025-06-14 09:07:15,154 - ib_insync.wrapper - INFO - Warning 2104, reqId -1: \u5e02\u573a\u6570\u636e\u519c\u573a\u8fde\u63a5\u6b63\u5e38:hfarm
2025-06-14 09:07:15,155 - ib_insync.wrapper - INFO - Warning 2106, reqId -1: \u5386\u53f2\u5e02\u573a\u6570\u636e\u519c\u573a\u8fde\u63a5\u6b63\u5e38:apachmds
2025-06-14 09:07:15,155 - ib_insync.wrapper - INFO - Warning 2158, reqId -1: Sec-def\u6570\u636e\u519c\u573a\u8fde\u63a5\u6b63\u5e38:secdefhk
2025-06-14 09:07:15,247 - ib_insync.wrapper - INFO - position: Position(account='DUK362830', contract=Stock(conId=*********, symbol='EICB', exchange='NYSE', currency='USD', localSymbol='EICB', tradingClass='EICB'), position=134.0, avgCost=24.********)
2025-06-14 09:07:15,247 - ib_insync.wrapper - INFO - position: Position(account='DUK362830', contract=Stock(conId=*********, symbol='RCC', exchange='NYSE', currency='USD', localSymbol='RCC', tradingClass='RCC'), position=136.0, avgCost=24.********)
2025-06-14 09:07:15,247 - ib_insync.wrapper - INFO - position: Position(account='DUK362830', contract=Stock(conId=*********, symbol='FSHP', exchange='ISLAND', currency='USD', localSymbol='FSHP', tradingClass='NMS'), position=319.0, avgCost=10.5450351)
2025-06-14 09:07:15,247 - ib_insync.wrapper - INFO - position: Position(account='DUK362830', contract=Stock(conId=********, symbol='GJS', exchange='NYSE', currency='USD', localSymbol='GJS', tradingClass='GJS'), position=148.0, avgCost=22.9167919)
2025-06-14 09:07:15,247 - ib_insync.wrapper - INFO - position: Position(account='DUK362830', contract=Stock(conId=*********, symbol='WTFCP', exchange='ISLAND', currency='USD', localSymbol='WTFCP', tradingClass='NMS'), position=0.0, avgCost=0.0)
2025-06-14 09:07:15,247 - ib_insync.wrapper - INFO - position: Position(account='DUK362830', contract=Stock(conId=272093, symbol='MSFT', exchange='ISLAND', currency='USD', localSymbol='MSFT', tradingClass='NMS'), position=7.0, avgCost=472.9429)
2025-06-14 09:07:15,248 - ib_insync.wrapper - INFO - position: Position(account='DUK362830', contract=Stock(conId=*********, symbol='HROWL', exchange='ISLAND', currency='USD', localSymbol='HROWL', tradingClass='NMS'), position=132.0, avgCost=25.2876106)
2025-06-14 09:07:15,248 - ib_insync.wrapper - INFO - position: Position(account='DUK362830', contract=Stock(conId=*********, symbol='SSSSL', exchange='ISLAND', currency='USD', localSymbol='SSSSL', tradingClass='NMS'), position=0.0, avgCost=0.0)
2025-06-14 09:07:15,248 - ib_insync.wrapper - INFO - position: Position(account='DUK362830', contract=Stock(conId=********, symbol='GJT', exchange='NYSE', currency='USD', localSymbol='GJT', tradingClass='GJT'), position=147.0, avgCost=22.8068374)
2025-06-14 09:07:15,248 - ib_insync.wrapper - INFO - position: Position(account='DUK362830', contract=Stock(conId=*********, symbol='RWAYL', exchange='ISLAND', currency='USD', localSymbol='RWAYL', tradingClass='NMS'), position=133.0, avgCost=25.********)
2025-06-14 09:07:15,248 - ib_insync.wrapper - INFO - position: Position(account='DUK362830', contract=Stock(conId=10754, symbol='OHI', exchange='NYSE', currency='USD', localSymbol='OHI', tradingClass='OHI'), position=180.0, avgCost=36.9761464)
2025-06-14 09:07:15,248 - ib_insync.wrapper - INFO - position: Position(account='DUK362830', contract=Stock(conId=********, symbol='ALE', exchange='NYSE', currency='USD', localSymbol='ALE', tradingClass='ALE'), position=51.0, avgCost=65.********)
2025-06-14 09:07:15,248 - ib_insync.wrapper - INFO - position: Position(account='DUK362830', contract=Stock(conId=*********, symbol='GPAT', exchange='ISLAND', currency='USD', localSymbol='GPAT', tradingClass='NMS'), position=314.0, avgCost=10.655035)
2025-06-14 09:07:15,248 - ib_insync.wrapper - INFO - position: Position(account='DUK362830', contract=Stock(conId=*********, symbol='MITN', exchange='NYSE', currency='USD', localSymbol='MITN', tradingClass='MITN'), position=399.0, avgCost=25.********)
2025-06-14 09:07:15,248 - ib_insync.wrapper - INFO - position: Position(account='DUK362830', contract=Stock(conId=*********, symbol='TECTP', exchange='ISLAND', currency='USD', localSymbol='TECTP', tradingClass='SCM'), position=0.0, avgCost=0.0)
2025-06-14 09:07:15,248 - ib_insync.wrapper - INFO - position: Position(account='DUK362830', contract=Stock(conId=*********, symbol='SAZ', exchange='NYSE', currency='USD', localSymbol='SAZ', tradingClass='SAZ'), position=264.0, avgCost=25.2476107)
2025-06-14 09:07:15,548 - ib_insync.wrapper - INFO - updatePortfolio: PortfolioItem(contract=Stock(conId=********, symbol='ALE', right='0', primaryExchange='NYSE', currency='USD', localSymbol='ALE', tradingClass='ALE'), position=51.0, marketPrice=64.********, marketValue=3304.23, averageCost=65.********, unrealizedPNL=-33.2, realizedPNL=0.0, account='DUK362830')
2025-06-14 09:07:15,549 - ib_insync.wrapper - INFO - updatePortfolio: PortfolioItem(contract=Stock(conId=*********, symbol='EICB', right='0', primaryExchange='NYSE', currency='USD', localSymbol='EICB', tradingClass='EICB'), position=134.0, marketPrice=24.********, marketValue=3332.47, averageCost=24.********, unrealizedPNL=-3.8, realizedPNL=0.0, account='DUK362830')
2025-06-14 09:07:15,549 - ib_insync.wrapper - INFO - updatePortfolio: PortfolioItem(contract=Stock(conId=*********, symbol='FSHP', right='0', primaryExchange='ISLAND', currency='USD', localSymbol='FSHP', tradingClass='NMS'), position=319.0, marketPrice=10.********, marketValue=3339.79, averageCost=10.5450351, unrealizedPNL=-24.07, realizedPNL=0.0, account='DUK362830')
2025-06-14 09:07:15,550 - ib_insync.wrapper - INFO - updatePortfolio: PortfolioItem(contract=Stock(conId=********, symbol='GJS', right='0', primaryExchange='NYSE', currency='USD', localSymbol='GJS', tradingClass='GJS'), position=148.0, marketPrice=22.********, marketValue=3359.6, averageCost=22.9167919, unrealizedPNL=-32.09, realizedPNL=0.0, account='DUK362830')
2025-06-14 09:07:15,550 - ib_insync.wrapper - INFO - updatePortfolio: PortfolioItem(contract=Stock(conId=********, symbol='GJT', right='0', primaryExchange='NYSE', currency='USD', localSymbol='GJT', tradingClass='GJT'), position=147.0, marketPrice=22.********, marketValue=3321.96, averageCost=22.8068374, unrealizedPNL=-30.65, realizedPNL=0.0, account='DUK362830')
2025-06-14 09:07:15,550 - ib_insync.wrapper - INFO - updatePortfolio: PortfolioItem(contract=Stock(conId=*********, symbol='GPAT', right='0', primaryExchange='ISLAND', currency='USD', localSymbol='GPAT', tradingClass='NMS'), position=314.0, marketPrice=10.********, marketValue=3326.96, averageCost=10.655035, unrealizedPNL=-18.72, realizedPNL=0.0, account='DUK362830')
2025-06-14 09:07:15,550 - ib_insync.wrapper - INFO - updatePortfolio: PortfolioItem(contract=Stock(conId=*********, symbol='HROWL', right='0', primaryExchange='ISLAND', currency='USD', localSymbol='HROWL', tradingClass='NMS'), position=132.0, marketPrice=25.********, marketValue=3344.52, averageCost=25.2876106, unrealizedPNL=6.56, realizedPNL=0.0, account='DUK362830')
2025-06-14 09:07:15,550 - ib_insync.wrapper - INFO - updatePortfolio: PortfolioItem(contract=Stock(conId=*********, symbol='MITN', right='0', primaryExchange='NYSE', currency='USD', localSymbol='MITN', tradingClass='MITN'), position=399.0, marketPrice=25.********, marketValue=9988.37, averageCost=25.********, unrealizedPNL=-0.29, realizedPNL=0.0, account='DUK362830')
2025-06-14 09:07:15,551 - ib_insync.wrapper - INFO - updatePortfolio: PortfolioItem(contract=Stock(conId=272093, symbol='MSFT', right='0', primaryExchange='ISLAND', currency='USD', localSymbol='MSFT', tradingClass='NMS'), position=7.0, marketPrice=474.********, marketValue=3320.73, averageCost=472.9429, unrealizedPNL=10.13, realizedPNL=0.0, account='DUK362830')
2025-06-14 09:07:15,551 - ib_insync.wrapper - INFO - updatePortfolio: PortfolioItem(contract=Stock(conId=10754, symbol='OHI', right='0', primaryExchange='NYSE', currency='USD', localSymbol='OHI', tradingClass='OHI'), position=180.0, marketPrice=37.0288925, marketValue=6665.2, averageCost=36.9761464, unrealizedPNL=9.49, realizedPNL=0.0, account='DUK362830')
2025-06-14 09:07:15,551 - ib_insync.wrapper - INFO - updatePortfolio: PortfolioItem(contract=Stock(conId=*********, symbol='RCC', right='0', primaryExchange='NYSE', currency='USD', localSymbol='RCC', tradingClass='RCC'), position=136.0, marketPrice=24.445488, marketValue=3324.59, averageCost=24.********, unrealizedPNL=-4.34, realizedPNL=0.0, account='DUK362830')
2025-06-14 09:07:15,551 - ib_insync.wrapper - INFO - updatePortfolio: PortfolioItem(contract=Stock(conId=*********, symbol='RWAYL', right='0', primaryExchange='ISLAND', currency='USD', localSymbol='RWAYL', tradingClass='NMS'), position=133.0, marketPrice=25.0811329, marketValue=3335.79, averageCost=25.********, unrealizedPNL=-4.84, realizedPNL=0.0, account='DUK362830')
2025-06-14 09:07:15,551 - ib_insync.wrapper - INFO - updatePortfolio: PortfolioItem(contract=Stock(conId=*********, symbol='SAZ', right='0', primaryExchange='NYSE', currency='USD', localSymbol='SAZ', tradingClass='SAZ'), position=264.0, marketPrice=25.********, marketValue=6655.47, averageCost=25.2476107, unrealizedPNL=-9.9, realizedPNL=0.0, account='DUK362830')
2025-06-14 09:07:15,551 - ib_insync.wrapper - INFO - updatePortfolio: PortfolioItem(contract=Stock(conId=*********, symbol='SSSSL', right='0', primaryExchange='ISLAND', currency='USD', localSymbol='SSSSL', tradingClass='NMS'), position=0.0, marketPrice=24.5400181, marketValue=0.0, averageCost=0.0, unrealizedPNL=0.0, realizedPNL=-118.64, account='DUK362830')
2025-06-14 09:07:15,552 - ib_insync.wrapper - INFO - updatePortfolio: PortfolioItem(contract=Stock(conId=*********, symbol='TECTP', right='0', primaryExchange='ISLAND', currency='USD', localSymbol='TECTP', tradingClass='SCM'), position=0.0, marketPrice=10.5975952, marketValue=0.0, averageCost=0.0, unrealizedPNL=0.0, realizedPNL=-34.83, account='DUK362830')
2025-06-14 09:07:15,552 - ib_insync.wrapper - INFO - updatePortfolio: PortfolioItem(contract=Stock(conId=*********, symbol='WTFCP', right='0', primaryExchange='ISLAND', currency='USD', localSymbol='WTFCP', tradingClass='NMS'), position=0.0, marketPrice=25.3009777, marketValue=0.0, averageCost=0.0, unrealizedPNL=0.0, realizedPNL=-1.03, account='DUK362830')
2025-06-14 09:07:15,652 - ib_insync.wrapper - INFO - execDetails Execution(execId='00025b47.684bba2c.01.01', time=datetime.datetime(2025, 6, 13, 18, 42, 49, tzinfo=datetime.timezone.utc), acctNumber='DUK362830', exchange='SMART', side='SLD', shares=131.0, price=25.29, permId=*********, clientId=0, orderId=0, liquidation=0, cumQty=131.0, avgPrice=25.29, orderRef='', evRule='', evMultiplier=0.0, modelCode='', lastLiquidity=2)
2025-06-14 09:07:15,653 - ib_insync.wrapper - INFO - execDetails Execution(execId='00025b46.684c65e7.01.01', time=datetime.datetime(2025, 6, 13, 18, 43, 22, tzinfo=datetime.timezone.utc), acctNumber='DUK362830', exchange='DRCTEDGE', side='SLD', shares=316.0, price=10.55, permId=*********, clientId=0, orderId=0, liquidation=0, cumQty=316.0, avgPrice=10.55, orderRef='', evRule='', evMultiplier=0.0, modelCode='', lastLiquidity=2)
2025-06-14 09:07:15,653 - ib_insync.wrapper - INFO - execDetails Execution(execId='00025b49.684d832a.01.01', time=datetime.datetime(2025, 6, 13, 18, 44, 4, tzinfo=datetime.timezone.utc), acctNumber='DUK362830', exchange='SMART', side='SLD', shares=200.0, price=24.42, permId=210530219, clientId=0, orderId=0, liquidation=0, cumQty=200.0, avgPrice=24.42, orderRef='', evRule='', evMultiplier=0.0, modelCode='', lastLiquidity=2)
2025-06-14 09:07:15,653 - ib_insync.wrapper - INFO - execDetails Execution(execId='00025b49.684daa37.01.01', time=datetime.datetime(2025, 6, 13, 20, 0, tzinfo=datetime.timezone.utc), acctNumber='DUK362830', exchange='SMART', side='SLD', shares=68.0, price=24.42, permId=210530219, clientId=0, orderId=0, liquidation=0, cumQty=268.0, avgPrice=24.42, orderRef='', evRule='', evMultiplier=0.0, modelCode='', lastLiquidity=2)
2025-06-14 09:07:15,654 - ib_insync.wrapper - INFO - commissionReport: CommissionReport(execId='00025b47.684bba2c.01.01', commission=1.026331, currency='USD', realizedPNL=-1.030916, yield_=0.0, yieldRedemptionDate=0)
2025-06-14 09:07:15,654 - ib_insync.wrapper - INFO - commissionReport: CommissionReport(execId='00025b46.684c65e7.01.01', commission=1.643516, currency='USD', realizedPNL=-34.834616, yield_=0.0, yieldRedemptionDate=0)
2025-06-14 09:07:15,654 - ib_insync.wrapper - INFO - commissionReport: CommissionReport(execId='00025b49.684d832a.01.01', commission=1.0402, currency='USD', realizedPNL=-88.539752, yield_=0.0, yieldRedemptionDate=0)
2025-06-14 09:07:15,654 - ib_insync.wrapper - INFO - commissionReport: CommissionReport(execId='00025b49.684daa37.01.01', commission=0.353668, currency='USD', realizedPNL=-30.103516, yield_=0.0, yieldRedemptionDate=0)
2025-06-14 09:07:15,654 - ib_insync.ib - INFO - Synchronization complete
2025-06-14 09:07:15,654 - IBKRClient - INFO - Connected to IBKR at 127.0.0.1:7497
2025-06-14 09:07:15,700 - IBKRClient - INFO - Account type: INDIVIDUAL
2025-06-14 09:07:15,700 - ibkr_data_provider - INFO - Connected to IBKR for sequential downloading (optimized for data farm stability)
2025-06-14 09:07:15,702 - ibkr_data_provider - INFO - 🚀 Starting optimized concurrent download
2025-06-14 09:07:15,702 - ibkr_data_provider - INFO - 📊 Total stocks: 5915 (cached: 0, downloading: 5915)
2025-06-14 09:07:15,702 - ibkr_data_provider - INFO - ⚡ Adaptive concurrent limit: 1
2025-06-14 09:07:15,702 - ibkr_data_provider - INFO - 📅 Date range: 2024-06-14 to 2025-06-14
2025-06-14 09:07:15,704 - ibkr_data_provider - INFO - 📂 Progress loaded: 5539 completed, 376 failed, 0 invalid
2025-06-14 09:07:15,704 - ibkr_data_provider - INFO - 🔄 Resuming download from symbol 5915/5915
2025-06-14 09:07:15,704 - ibkr_data_provider - INFO - 📊 Previous progress: 5539 completed, 376 failed
2025-06-14 09:07:15,706 - ibkr_data_provider - INFO - ✅ All data available from previous downloads, loading from incremental data...
2025-06-14 09:07:15,706 - ibkr_data_provider - INFO - ⚠️  No incremental data found, trying to load from legacy data.pickle...
2025-06-14 09:07:15,706 - ibkr_data_provider - INFO - 📦 No data.pickle file found
2025-06-14 09:07:15,706 - ibkr_data_provider - WARNING - ⚠️  No legacy data found either, will proceed with empty cache
2025-06-14 09:07:15,706 - ibkr_data_provider - INFO - ✅ All data available from cache!
2025-06-14 09:07:15,707 - ibkr_data_provider - INFO - 🎯 Sequential download completed!
2025-06-14 09:07:15,707 - ibkr_data_provider - INFO - ✅ Successful: 5539
2025-06-14 09:07:15,707 - ibkr_data_provider - INFO - ❌ Failed: 376
2025-06-14 09:07:15,707 - ibkr_data_provider - INFO - 🚫 Invalid symbols: 0
2025-06-14 09:07:15,707 - ibkr_data_provider - INFO - 📈 Success rate: 93.6%
2025-06-14 09:07:15,707 - ibkr_data_provider - INFO - ⚡ Download rate: 1258245.8 stocks/second
2025-06-14 09:07:15,707 - ibkr_data_provider - INFO - ⏱️  Total time: 0.0 seconds
2025-06-14 09:07:15,707 - ibkr_data_provider - INFO - 📂 Resume capability: Progress saved for 5539 symbols
2025-06-14 09:07:15,707 - ibkr_data_provider - INFO - 💾 Progress saved - can resume with 376 failed symbols
2025-06-14 09:07:15,707 - __main__ - INFO - ✅ IBKR connection transferred to main client - keeping alive for trading
2025-06-14 09:07:15,707 - __main__ - ERROR - Failed to download stock data
2025-06-14 09:07:15,708 - __main__ - ERROR - Step 1 failed - aborting workflow
2025-06-14 09:07:15,779 - ib_insync.ib - INFO - Disconnecting from 127.0.0.1:7497, 713 B sent in 9 messages, 35.3 kB received in 700 messages, session time 652 ms.
2025-06-14 09:07:15,780 - ib_insync.client - INFO - Disconnecting
