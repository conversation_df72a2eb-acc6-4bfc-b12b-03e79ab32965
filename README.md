# 🚀 智能股票交易系统 (Intelligent Stock Trading System)

一个基于机器学习和Interactive Brokers API的全自动股票交易系统，集成了数据下载、模型训练、趋势分析、投资组合管理和实时交易功能。

## 📋 目录

- [系统概述](#系统概述)
- [核心功能](#核心功能)
- [系统架构](#系统架构)
- [安装配置](#安装配置)
- [配置参数](#配置参数)
- [使用指南](#使用指南)
- [模块详解](#模块详解)
- [交易策略](#交易策略)
- [风险管理](#风险管理)
- [API参考](#api参考)

## 🎯 系统概述

本系统是一个完整的量化交易解决方案，具备以下特点：

- **数据驱动**：自动从IBKR下载3年历史数据，支持5000+股票
- **机器学习**：基于MSIS-MCS模型的多层次趋势分析
- **实时交易**：集成IBKR API，支持纸上交易和实盘交易
- **风险控制**：智能止损系统和投资组合管理
- **多策略**：6种不同的交易机器人策略
- **回归预测**：CNN-GRU混合模型预测股票相关性回归时间

## ⭐ 核心功能

### 1. 数据管理系统
- **历史数据下载**：自动下载NYSE/NASDAQ股票3年历史数据
- **实时数据更新**：增量更新机制，避免重复下载
- **股票分类**：自动获取行业和板块分类信息
- **数据验证**：自动验证和清理无效股票代码

### 2. 机器学习模型
- **MSIS-MCS模型**：多尺度多层次股票趋势预测
- **回归时间预测**：CNN-GRU混合模型预测相关性回归
- **7级趋势分类**：从EXTREME BELOW到EXTREME ABOVE的精细分类
- **相关性分析**：基于ML的股票相关性匹配

### 3. 交易执行系统
- **多策略机器人**：Adam、Betty、Chris、Dany、Eddy、Flora
- **实时交易**：支持IBKR纸上交易和实盘交易
- **智能止损**：动态止损和止盈系统
- **投资组合管理**：风险分散和仓位控制

### 4. 风险管理
- **动态止损**：基于趋势变化的智能止损
- **仓位控制**：最大单股仓位限制
- **板块分散**：自动板块风险分散
- **资金管理**：可配置的资金分配策略

## 🏗️ 系统架构

```
智能交易系统/
├── 数据层 (Data Layer)
│   ├── ibkr_data_provider.py      # IBKR数据下载
│   ├── update_stock_universe.py   # 股票池更新
│   └── data_store/                # 数据存储目录
├── 模型层 (Model Layer)
│   ├── models.py                  # MSIS-MCS模型
│   ├── enhanced_reversion_prediction.py  # 回归预测模型
│   └── volatile.py                # 趋势分析
├── 交易层 (Trading Layer)
│   ├── daily_trading_system.py    # 主交易系统
│   ├── bots.py                    # 交易策略机器人
│   └── portfolio_manager.py       # 投资组合管理
├── 连接层 (Connection Layer)
│   ├── ibkr_client.py             # IBKR API客户端
│   └── config.py                  # 配置管理
└── 工具层 (Utility Layer)
    ├── tools.py                   # 工具函数
    └── tournament.py              # 策略比较
```

## 🔧 安装配置

### 环境要求
```bash
Python >= 3.8
TensorFlow >= 2.8
Interactive Brokers TWS/Gateway
```

### 依赖安装
```bash
# 核心依赖
pip install tensorflow tensorflow-probability
pip install ib_insync pandas numpy matplotlib
pip install scikit-learn yfinance requests

# 技术指标库（可选，提升性能）
pip install ta-lib

# 异步支持
pip install nest-asyncio aiohttp
```

### IBKR配置
1. **安装TWS或Gateway**：从Interactive Brokers官网下载
2. **启用API**：在TWS中启用API连接
3. **配置端口**：
   - 纸上交易：7497
   - 实盘交易：7496
4. **设置权限**：确保API有交易权限

## ⚙️ 配置参数

### 主要配置文件：`config.py`

#### IBKR连接配置
```python
class IBKRConfig:
    host: str = "127.0.0.1"          # IBKR主机地址
    port: int = 7497                 # 端口号（7497=纸上，7496=实盘）
    client_id: int = None            # 客户端ID（自动分配）
    paper_trading: bool = True       # 是否纸上交易
    timeout: int = 30                # 连接超时时间
    max_requests_per_second: int = 50 # 最大请求频率
```

#### 数据下载配置
```python
class IBKRPerformanceConfig:
    max_concurrent_requests: int = 50     # 最大并发请求数
    request_delay: float = 0.02          # 请求间隔（秒）
    retry_attempts: int = 3              # 重试次数
    chunk_size: int = 100                # 分块大小
    enable_caching: bool = True          # 启用缓存
    cache_duration_hours: int = 24       # 缓存有效期
```

#### 交易系统配置
```python
# 在 daily_trading_system.py 中
TRADING_CONFIG = {
    'max_position_size': 0.05,           # 最大单股仓位（5%）
    'max_sector_exposure': 0.3,          # 最大板块暴露（30%）
    'stop_loss_threshold': 0.03,         # 止损阈值（3%）
    'take_profit_threshold': 0.1,        # 止盈阈值（10%）
    'min_trade_amount': 1000,            # 最小交易金额
    'max_daily_trades': 50,              # 每日最大交易数
}
```

#### 模型训练配置
```python
# 在 enhanced_reversion_prediction.py 中
MODEL_CONFIG = {
    'model_type': 'cnn_gru',             # 模型类型：'lstm' 或 'cnn_gru'
    'cnn_filters': [64, 32],             # CNN滤波器数量
    'gru_units': [128, 64],              # GRU单元数
    'dropout_rate': 0.3,                 # Dropout比率
    'learning_rate': 0.0005,             # 学习率
    'batch_size': 64,                    # 批次大小
    'epochs': 200,                       # 训练轮数
    'early_stopping_patience': 30,       # 早停耐心值
}
```

### 趋势分类配置
```python
# 7级趋势分类阈值
TREND_THRESHOLDS = {
    'EXTREME_BELOW': (0.1, 0.6),        # 极度低于趋势：0.1-0.6%
    'HIGHLY_BELOW': (1, 2),             # 高度低于趋势：1-2%
    'BELOW_TREND': (5, 8),              # 低于趋势：5-8%
    'ALONG_TREND': (38, 79),            # 沿趋势：38-79%
    'ABOVE_TREND': (5, 8),              # 高于趋势：5-8%
    'HIGHLY_ABOVE': (1, 2),             # 高度高于趋势：1-2%
    'EXTREME_ABOVE': (0.1, 0.6),        # 极度高于趋势：0.1-0.6%
}
```

## 📖 使用指南

### 1. 快速开始

#### 启动完整交易系统
```bash
# 纸上交易模式
python daily_trading_system.py --paper

# 实盘交易模式（谨慎使用）
python daily_trading_system.py --live

# 指定机器人策略
python daily_trading_system.py --paper --bot Betty

# 指定资金量
python daily_trading_system.py --paper --capital 100000
```

#### 更新股票池
```bash
# 更新NYSE/NASDAQ股票列表
python update_stock_universe.py

# 限制股票数量
python update_stock_universe.py --max-symbols 1000
```

#### 下载历史数据
```bash
# 下载3年历史数据
python ibkr_data_provider.py

# 指定时间范围
python ibkr_data_provider.py --start 2021-01-01 --end 2024-01-01
```

### 2. 模型训练

#### 训练回归预测模型
```bash
# 使用真实数据训练CNN-GRU模型
python enhanced_reversion_prediction.py

# 模型性能比较
python model_comparison_test.py

# 简化测试
python simple_model_test.py
```

#### 策略回测
```bash
# 运行策略锦标赛
python tournament.py --start 2023-01-01 --end 2024-01-01

# 单策略回测
python tournament.py --bot Betty --capital 50000
```

### 3. 实时监控

#### 查看交易日志
```bash
# 实时查看日志
tail -f results/YYYYMMDD/logs/trading_YYYYMMDD_HHMMSS.log

# 查看投资组合状态
python -c "
from portfolio_manager import PortfolioManager
pm = PortfolioManager()
print(pm.get_portfolio_summary())
"
```

#### 监控系统状态
```bash
# 检查IBKR连接
python -c "
from ibkr_client import IBKRClient
import asyncio
async def check():
    client = IBKRClient()
    connected = await client.connect()
    print(f'IBKR连接状态: {connected}')
asyncio.run(check())
"
```

## 🤖 模块详解

### 1. 数据管理模块

#### `ibkr_data_provider.py` - IBKR数据下载器
**主要功能**：
- 并发下载历史数据（最大50并发）
- 增量更新机制
- 自动重连和错误恢复
- 数据缓存和压缩

**关键方法**：
```python
async def download_multiple_stocks(tickers, start, end, max_concurrent=50)
async def download_single_stock(ticker, start_date, end_date)
def _load_incremental_data()
def _save_incremental_data(data)
```

#### `update_stock_universe.py` - 股票池管理器
**主要功能**：
- 获取NYSE/NASDAQ完整股票列表
- 股票有效性验证
- 行业板块分类
- 自动清理无效代码

**关键方法**：
```python
async def get_enhanced_symbol_sources()
async def validate_and_classify_stocks(symbols)
async def run_full_update(max_symbols=None)
```

### 2. 机器学习模块

#### `models.py` - MSIS-MCS模型
**主要功能**：
- 多尺度多层次股票建模
- 贝叶斯推理
- 趋势预测

**模型层次**：
- Market Level（市场层）
- Sector Level（板块层）
- Industry Level（行业层）
- Stock Level（个股层）

#### `enhanced_reversion_prediction.py` - 回归预测模型
**主要功能**：
- CNN-GRU混合架构
- 技术指标特征工程
- 相关性背离检测
- 回归时间预测

**模型架构**：
```python
# CNN层：提取技术指标局部模式
Conv1D(filters=64, kernel_size=3) -> BatchNorm -> Dropout
Conv1D(filters=32, kernel_size=3) -> BatchNorm -> Dropout

# GRU层：处理时序依赖
GRU(units=128) -> GRU(units=64) -> BatchNorm

# 融合层：结合静态特征
Concatenate([time_series_features, static_features])

# 输出层：预测回归天数
Dense(128) -> Dense(64) -> Dense(32) -> Dense(1)
```

### 3. 交易执行模块

#### `daily_trading_system.py` - 主交易系统
**主要功能**：
- 数据下载和模型训练
- 趋势分析和信号生成
- 交易执行和风险控制
- 结果记录和报告

**执行流程**：
1. 数据下载和验证
2. 模型训练和预测
3. 趋势分析和分类
4. 交易信号生成
5. 风险检查和执行
6. 结果记录和报告

#### `bots.py` - 交易策略机器人
**策略详解**：

**Adam机器人**：
- 策略：买入HIGHLY BELOW TREND，卖出HIGHLY ABOVE TREND
- 风险：保守型，适合稳定收益

**Betty机器人**：
- 策略：动态止盈止损
- 止盈：10%
- 止损：3%
- 风险：中等，平衡收益和风险

**Chris机器人**：
- 策略：专注大盘股（GOOGL, AMZN, AAPL, MSFT, META）
- 风险：低风险，适合保守投资者

**Dany机器人**：
- 策略：快速交易，短期持有
- 风险：高风险高收益

**Eddy机器人**：
- 策略：反向投资
- 风险：高风险，适合经验丰富的投资者

**Flora机器人**：
- 策略：长期持有价值投资
- 风险：低风险，适合长期投资

### 4. 风险管理模块

#### `portfolio_manager.py` - 投资组合管理器
**主要功能**：
- 实时仓位监控
- 动态止损止盈
- 风险分散控制
- 业绩分析报告

**风险控制指标**：
```python
# 仓位控制
max_position_weight = 5%           # 单股最大仓位
max_sector_exposure = 30%          # 单板块最大暴露

# 止损止盈
stop_loss_threshold = 3%           # 止损阈值
take_profit_threshold = 10%        # 止盈阈值
trailing_stop_distance = 2%        # 跟踪止损距离

# 风险指标
portfolio_beta = 1.2               # 投资组合贝塔
sharpe_ratio = 1.5                 # 夏普比率
max_drawdown = 5%                  # 最大回撤
```

## 📊 交易策略

### 趋势跟踪策略
基于MSIS-MCS模型的7级趋势分类：

1. **EXTREME BELOW TREND** (0.1-0.6%)
   - 极度超卖，强烈买入信号
   - 适合：激进投资者

2. **HIGHLY BELOW TREND** (1-2%)
   - 高度超卖，买入信号
   - 适合：大多数策略机器人

3. **BELOW TREND** (5-8%)
   - 轻度超卖，谨慎买入
   - 适合：保守投资者

4. **ALONG TREND** (38-79%)
   - 正常趋势，持有观望
   - 适合：长期投资者

5. **ABOVE TREND** (5-8%)
   - 轻度超买，考虑减仓
   - 适合：获利了结

6. **HIGHLY ABOVE TREND** (1-2%)
   - 高度超买，卖出信号
   - 适合：大多数策略机器人

7. **EXTREME ABOVE TREND** (0.1-0.6%)
   - 极度超买，强烈卖出信号
   - 适合：激进交易者

### 相关性交易策略
基于CNN-GRU回归预测模型：

1. **背离识别**：
   - 检测高相关性股票对的价格背离
   - 量化背离程度和持续时间

2. **回归预测**：
   - 预测背离回归的时间窗口
   - 为期权交易提供时间指导

3. **期权策略**：
   - 在背离时买入期权
   - 在预测回归时间前平仓

## 🛡️ 风险管理

### 系统性风险控制

#### 1. 仓位管理
```python
# 单股仓位限制
max_single_position = total_capital * 0.05  # 5%

# 板块暴露限制
max_sector_exposure = total_capital * 0.3   # 30%

# 总仓位限制
max_total_exposure = total_capital * 0.95   # 95%
```

#### 2. 止损机制
```python
# 固定止损
if current_loss > position_value * 0.03:    # 3%
    trigger_stop_loss()

# 跟踪止损
if price_drop_from_peak > 0.02:             # 2%
    trigger_trailing_stop()

# 趋势止损
if trend_score < threshold:
    trigger_trend_stop()
```

#### 3. 流动性管理
```python
# 最小交易量要求
min_daily_volume = 1000000                   # 100万股

# 市场冲击控制
max_order_size = daily_volume * 0.01         # 1%日成交量

# 分批执行
if order_size > threshold:
    split_into_smaller_orders()
```

### 技术风险控制

#### 1. 连接监控
```python
# 连接状态检查
async def monitor_connection():
    if not client.connected:
        await client.reconnect()
        
# 心跳检测
async def heartbeat():
    await client.ping()
```

#### 2. 数据质量控制
```python
# 数据完整性检查
def validate_data(data):
    check_missing_values()
    check_outliers()
    check_data_consistency()

# 实时数据验证
def validate_market_data(price, volume):
    if price <= 0 or volume < 0:
        raise DataQualityError()
```

#### 3. 错误恢复
```python
# 自动重试机制
@retry(max_attempts=3, delay=1)
async def execute_trade(order):
    return await client.place_order(order)

# 故障转移
if primary_connection_failed:
    switch_to_backup_connection()
```

## 📚 API参考

### 主要类和方法

#### IBKRClient
```python
class IBKRClient:
    async def connect() -> bool
    async def disconnect()
    async def get_historical_data(symbol, duration, bar_size)
    async def place_order(contract, order)
    async def get_positions()
    async def get_account_info()
```

#### DailyTradingSystem
```python
class DailyTradingSystem:
    async def run_daily_analysis()
    async def download_data()
    def train_models()
    def analyze_trends()
    async def execute_trades()
    def generate_reports()
```

#### PortfolioManager
```python
class PortfolioManager:
    def add_position(symbol, units, price)
    def remove_position(symbol)
    def update_prices(price_data)
    def calculate_metrics()
    def check_stop_loss()
    def rebalance_portfolio()
```

### 配置示例

#### 完整配置文件示例
```python
# config_example.py
from config import IBKRConfig, IBKRPerformanceConfig

# IBKR连接配置
ibkr_config = IBKRConfig(
    host="127.0.0.1",
    port=7497,                    # 纸上交易
    paper_trading=True,
    timeout=30,
    max_requests_per_second=50
)

# 性能配置
performance_config = IBKRPerformanceConfig(
    max_concurrent_requests=50,
    request_delay=0.02,
    retry_attempts=3,
    enable_caching=True,
    cache_duration_hours=24
)

# 交易配置
trading_config = {
    'capital': 100000,
    'max_position_size': 0.05,
    'stop_loss_threshold': 0.03,
    'take_profit_threshold': 0.1,
    'bot_strategy': 'Betty'
}
```

## 🚨 注意事项

### 重要警告
1. **实盘交易风险**：实盘交易涉及真实资金，请充分测试后谨慎使用
2. **数据依赖**：系统依赖IBKR数据质量，请确保连接稳定
3. **监管合规**：请遵守当地金融监管法规
4. **技术风险**：算法交易存在技术风险，建议设置熔断机制

### 最佳实践
1. **先纸上交易**：充分测试策略后再考虑实盘
2. **小额开始**：实盘交易从小额资金开始
3. **定期监控**：定期检查系统状态和交易结果
4. **备份数据**：定期备份重要数据和配置
5. **版本控制**：使用Git管理代码版本

### 技术支持
- **日志文件**：`results/YYYYMMDD/logs/`
- **数据文件**：`data_store/`
- **配置文件**：`config.py`
- **错误排查**：检查IBKR连接状态和API权限

---

## 🔧 高级配置

### 文件结构详解
```
项目根目录/
├── 📁 data_store/                    # 数据存储目录
│   ├── stock_info.csv               # 股票信息（行业、板块）
│   ├── symbols_list.txt             # 有效股票代码列表
│   ├── daily_model_cache_*.pickle   # 模型缓存文件
│   └── incremental_data_*.pickle    # 增量数据文件
├── 📁 results/                      # 结果输出目录
│   └── YYYYMMDD/                    # 按日期组织的结果
│       ├── 📁 logs/                 # 日志文件
│       ├── 📁 charts/               # 图表文件
│       ├── daily_results_*.csv      # 每日分析结果
│       ├── trading_summary_*.csv    # 交易汇总
│       └── portfolio_*.csv          # 投资组合状态
├── 📄 config.py                     # 主配置文件
├── 📄 daily_trading_system.py       # 主交易系统
├── 📄 ibkr_client.py               # IBKR客户端
├── 📄 ibkr_data_provider.py        # 数据下载器
├── 📄 models.py                     # ML模型
├── 📄 bots.py                       # 交易机器人
├── 📄 portfolio_manager.py          # 投资组合管理
├── 📄 enhanced_reversion_prediction.py  # 回归预测
└── 📄 update_stock_universe.py      # 股票池更新
```

### 环境变量配置
```bash
# 创建 .env 文件
cat > .env << EOF
# IBKR配置
IBKR_HOST=127.0.0.1
IBKR_PAPER_PORT=7497
IBKR_LIVE_PORT=7496
IBKR_CLIENT_ID=1001

# 交易配置
DEFAULT_CAPITAL=100000
MAX_POSITION_SIZE=0.05
STOP_LOSS_THRESHOLD=0.03
TAKE_PROFIT_THRESHOLD=0.1

# 数据配置
DATA_STORE_PATH=./data_store
RESULTS_PATH=./results
CACHE_DURATION_HOURS=24

# 日志配置
LOG_LEVEL=INFO
LOG_FORMAT="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
EOF
```

### 定时任务配置
```bash
# 添加到 crontab
crontab -e

# 每个交易日早上8:30更新股票池
30 8 * * 1-5 cd /path/to/project && python update_stock_universe.py

# 每个交易日早上9:00执行交易
0 9 * * 1-5 cd /path/to/project && python daily_trading_system.py --paper --bot Betty

# 每个交易日下午4:30生成报告
30 16 * * 1-5 cd /path/to/project && python generate_daily_report.py

# 每周日凌晨2:00清理旧数据
0 2 * * 0 cd /path/to/project && python cleanup_old_data.py
```

## 🎛️ 详细配置参数

### 数据下载配置
```python
# ibkr_data_provider.py 中的配置
DATA_CONFIG = {
    # 下载参数
    'duration_years': 3,                    # 历史数据年数
    'bar_size': '1 day',                   # K线周期
    'what_to_show': 'ADJUSTED_LAST',       # 数据类型
    'use_rth': True,                       # 仅常规交易时间

    # 性能参数
    'max_concurrent': 50,                  # 最大并发数
    'request_delay': 0.02,                 # 请求间隔（秒）
    'chunk_size': 100,                     # 分块大小
    'retry_attempts': 3,                   # 重试次数
    'timeout': 30,                         # 超时时间

    # 缓存参数
    'enable_incremental': True,            # 启用增量更新
    'cache_compression': True,             # 启用压缩
    'backup_count': 5,                     # 备份文件数量

    # 数据质量控制
    'min_trading_days': 100,               # 最少交易天数
    'max_missing_ratio': 0.1,              # 最大缺失比例
    'outlier_threshold': 5,                # 异常值阈值（标准差倍数）
}
```

### 模型训练配置
```python
# models.py 中的 MSIS-MCS 模型配置
MSIS_MCS_CONFIG = {
    # 模型结构
    'order_scale': [1, 0.1, 0.01],         # 多尺度参数
    'num_levels': 4,                       # 层次数量（市场/板块/行业/个股）

    # 训练参数
    'learning_rate': 0.01,                 # 学习率
    'num_steps': 10000,                    # 训练步数
    'batch_size': 32,                      # 批次大小

    # 正则化
    'l1_regularization': 0.001,            # L1正则化
    'l2_regularization': 0.001,            # L2正则化
    'dropout_rate': 0.1,                   # Dropout比率

    # 收敛控制
    'tolerance': 1e-6,                     # 收敛容忍度
    'early_stopping': True,                # 早停
    'patience': 100,                       # 早停耐心值
}

# enhanced_reversion_prediction.py 中的回归预测配置
REVERSION_CONFIG = {
    # 数据预处理
    'correlation_threshold': 0.6,          # 相关性阈值
    'divergence_threshold': 1.5,           # 背离阈值
    'reversion_threshold': 0.5,            # 回归阈值
    'min_divergence_days': 3,              # 最小背离天数
    'max_divergence_days': 120,            # 最大背离天数

    # 技术指标
    'rsi_period': 14,                      # RSI周期
    'macd_fast': 12,                       # MACD快线
    'macd_slow': 26,                       # MACD慢线
    'macd_signal': 9,                      # MACD信号线
    'bb_period': 20,                       # 布林带周期
    'bb_std': 2,                          # 布林带标准差

    # CNN-GRU模型
    'cnn_filters': [64, 32],               # CNN滤波器
    'cnn_kernel_size': 3,                  # 卷积核大小
    'gru_units': [128, 64],                # GRU单元数
    'dropout_rate': 0.3,                   # Dropout比率
    'learning_rate': 0.0005,               # 学习率
    'batch_size': 64,                      # 批次大小
    'epochs': 200,                         # 训练轮数
}
```

### 交易机器人配置
```python
# bots.py 中各机器人的详细配置

# Adam机器人 - 保守型趋势跟踪
ADAM_CONFIG = {
    'buy_signals': ['HIGHLY_BELOW_TREND'],
    'sell_signals': ['HIGHLY_ABOVE_TREND'],
    'max_positions': 30,
    'position_size_ratio': 1/30,           # 每个仓位占总资金的1/30
    'hold_period_min': 1,                  # 最少持有天数
    'hold_period_max': 30,                 # 最多持有天数
}

# Betty机器人 - 动态止盈止损
BETTY_CONFIG = {
    'buy_signals': ['HIGHLY_BELOW_TREND', 'BELOW_TREND'],
    'sell_signals': ['HIGHLY_ABOVE_TREND', 'ABOVE_TREND'],
    'min_rel_profit': 0.1,                 # 最小止盈比例（10%）
    'max_rel_loss': 0.03,                  # 最大止损比例（3%）
    'trailing_stop': True,                 # 启用跟踪止损
    'trailing_distance': 0.02,             # 跟踪止损距离（2%）
}

# Chris机器人 - 大盘股专注
CHRIS_CONFIG = {
    'buy_only': ['GOOGL', 'AMZN', 'AAPL', 'MSFT', 'META'],
    'equal_weight': True,                  # 等权重分配
    'rebalance_frequency': 'weekly',       # 重新平衡频率
    'min_cash_ratio': 0.1,                # 最小现金比例
}

# Dany机器人 - 快速交易
DANY_CONFIG = {
    'buy_signals': ['EXTREME_BELOW_TREND', 'HIGHLY_BELOW_TREND'],
    'sell_signals': ['EXTREME_ABOVE_TREND', 'HIGHLY_ABOVE_TREND'],
    'max_hold_days': 5,                    # 最大持有天数
    'quick_profit_target': 0.05,           # 快速止盈目标（5%）
    'position_size_aggressive': True,       # 激进仓位管理
}

# Eddy机器人 - 反向投资
EDDY_CONFIG = {
    'contrarian_signals': True,            # 反向信号
    'buy_signals': ['EXTREME_ABOVE_TREND'],
    'sell_signals': ['EXTREME_BELOW_TREND'],
    'patience_factor': 2.0,                # 耐心因子
    'risk_tolerance': 'high',              # 高风险容忍度
}

# Flora机器人 - 长期价值投资
FLORA_CONFIG = {
    'value_metrics': ['P/E', 'P/B', 'ROE'], # 价值指标
    'buy_signals': ['BELOW_TREND', 'ALONG_TREND'],
    'min_hold_period': 30,                 # 最少持有30天
    'dividend_preference': True,           # 偏好分红股
    'sector_diversification': True,        # 板块分散
}
```

### 风险管理配置
```python
# portfolio_manager.py 中的风险控制配置
RISK_CONFIG = {
    # 仓位限制
    'max_single_position': 0.05,           # 单股最大仓位（5%）
    'max_sector_exposure': 0.3,            # 单板块最大暴露（30%）
    'max_total_exposure': 0.95,            # 最大总仓位（95%）
    'min_cash_reserve': 0.05,              # 最小现金储备（5%）

    # 止损止盈
    'global_stop_loss': 0.02,              # 全局止损（2%）
    'global_take_profit': 0.15,            # 全局止盈（15%）
    'trailing_stop_distance': 0.02,        # 跟踪止损距离（2%）
    'stop_loss_cooldown': 24,              # 止损冷却期（小时）

    # 流动性控制
    'min_daily_volume': 1000000,           # 最小日成交量
    'max_market_impact': 0.01,             # 最大市场冲击（1%）
    'order_split_threshold': 10000,        # 大单拆分阈值

    # 风险指标
    'max_portfolio_beta': 1.5,             # 最大投资组合贝塔
    'target_sharpe_ratio': 1.0,            # 目标夏普比率
    'max_drawdown_limit': 0.1,             # 最大回撤限制（10%）
    'var_confidence': 0.95,                # VaR置信度

    # 相关性控制
    'max_correlation': 0.8,                # 最大持仓相关性
    'correlation_window': 60,              # 相关性计算窗口
    'rebalance_threshold': 0.05,           # 重新平衡阈值
}
```

### 监控和报警配置
```python
# 系统监控配置
MONITORING_CONFIG = {
    # 性能监控
    'cpu_threshold': 80,                   # CPU使用率阈值（%）
    'memory_threshold': 80,                # 内存使用率阈值（%）
    'disk_threshold': 90,                  # 磁盘使用率阈值（%）

    # 连接监控
    'connection_timeout': 30,              # 连接超时（秒）
    'heartbeat_interval': 60,              # 心跳间隔（秒）
    'reconnect_attempts': 5,               # 重连尝试次数
    'reconnect_delay': 10,                 # 重连延迟（秒）

    # 交易监控
    'max_daily_loss': 0.05,                # 每日最大亏损（5%）
    'max_daily_trades': 100,               # 每日最大交易数
    'unusual_volume_threshold': 3,          # 异常成交量阈值（倍数）
    'price_gap_threshold': 0.1,            # 价格跳空阈值（10%）

    # 报警设置
    'email_alerts': True,                  # 启用邮件报警
    'sms_alerts': False,                   # 启用短信报警
    'alert_cooldown': 300,                 # 报警冷却期（秒）
    'critical_alerts': [                   # 关键报警类型
        'connection_lost',
        'large_loss',
        'system_error',
        'unusual_activity'
    ]
}
```

## 📈 性能优化

### 数据下载优化
```python
# 并发下载优化
async def optimize_concurrent_downloads():
    # 动态调整并发数
    if system_load < 0.5:
        max_concurrent = 100
    elif system_load < 0.8:
        max_concurrent = 50
    else:
        max_concurrent = 20

    # 智能重试策略
    retry_delays = [1, 2, 5, 10, 30]  # 指数退避

    # 连接池管理
    connection_pool_size = max_concurrent * 2
```

### 模型训练优化
```python
# GPU加速配置
GPU_CONFIG = {
    'use_gpu': True,                       # 启用GPU
    'gpu_memory_growth': True,             # 动态GPU内存分配
    'mixed_precision': True,               # 混合精度训练
    'distributed_training': False,         # 分布式训练
}

# 模型缓存优化
CACHE_CONFIG = {
    'model_cache_size': '2GB',             # 模型缓存大小
    'feature_cache_size': '1GB',           # 特征缓存大小
    'cache_compression': 'lz4',            # 缓存压缩算法
    'cache_ttl': 3600,                     # 缓存生存时间（秒）
}
```

### 内存优化
```python
# 内存管理配置
MEMORY_CONFIG = {
    'batch_processing': True,              # 批量处理
    'lazy_loading': True,                  # 懒加载
    'memory_mapping': True,                # 内存映射
    'garbage_collection': 'auto',          # 垃圾回收
    'max_memory_usage': '8GB',             # 最大内存使用
}
```

## 🔍 故障排除

### 常见问题及解决方案

#### 1. IBKR连接问题
```bash
# 检查TWS/Gateway状态
netstat -an | grep 7497

# 检查API设置
# TWS -> File -> Global Configuration -> API -> Settings
# 确保启用 "Enable ActiveX and Socket Clients"

# 重启TWS/Gateway
pkill -f "Trader Workstation"
# 重新启动TWS
```

#### 2. 数据下载失败
```python
# 检查数据完整性
python -c "
from ibkr_data_provider import IBKRDataProvider
provider = IBKRDataProvider()
provider.validate_data_integrity()
"

# 清理损坏的缓存
rm data_store/daily_model_cache_*.pickle
rm data_store/incremental_data_*.pickle
```

#### 3. 模型训练错误
```python
# 检查数据质量
python -c "
import pickle
with open('data_store/daily_model_cache_latest.pickle', 'rb') as f:
    data = pickle.load(f)
print(f'Data shape: {data[\"price_data\"][\"price\"].shape}')
print(f'NaN count: {np.isnan(data[\"price_data\"][\"price\"]).sum()}')
"

# 重新训练模型
python daily_trading_system.py --retrain-models
```

#### 4. 交易执行问题
```python
# 检查账户状态
python -c "
from ibkr_client import IBKRClient
import asyncio
async def check_account():
    client = IBKRClient()
    await client.connect()
    account_info = await client.get_account_info()
    print(account_info)
asyncio.run(check_account())
"

# 检查订单状态
python -c "
from portfolio_manager import PortfolioManager
pm = PortfolioManager()
orders = pm.get_pending_orders()
print(f'Pending orders: {len(orders)}')
"
```

### 日志分析
```bash
# 查看错误日志
grep -i error results/*/logs/*.log | tail -20

# 查看交易日志
grep -i "trade" results/*/logs/*.log | tail -20

# 查看连接日志
grep -i "connect" results/*/logs/*.log | tail -20

# 实时监控日志
tail -f results/$(date +%Y%m%d)/logs/trading_*.log
```

### 性能监控
```bash
# 系统资源监控
top -p $(pgrep -f "python.*daily_trading")

# 网络连接监控
netstat -an | grep -E "(7496|7497)"

# 磁盘空间监控
df -h data_store/ results/

# 内存使用监控
ps aux | grep -E "(python.*daily_trading|TWS)"
```

---

**免责声明**：本系统仅供学习和研究使用，不构成投资建议。使用者需自行承担投资风险。
