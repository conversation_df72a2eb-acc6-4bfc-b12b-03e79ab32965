#!/usr/bin/env python
"""
Stock Universe Updater - Comprehensive stock symbols and classification management
Integrates symbol list updates and stock classification information
"""
import asyncio
import pandas as pd
import csv
import requests
import time
import os
from datetime import datetime, timed<PERSON>ta
from typing import Set, Dict, List, Optional
import logging

from ibkr_client import IBKRClient
from config import IBKRConfig, DEFAULT_CONFIG

# Setup logging
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)


class StockUniverseUpdater:
    """
    Comprehensive stock universe management system
    - Fetches symbols from multiple sources
    - Validates symbols with IBKR
    - Updates both symbols_list.txt and stock_info.csv
    - Maintains sector and industry classifications
    """

    def __init__(self, config: IBKRConfig = None):
        self.config = config or DEFAULT_CONFIG
        self.ibkr_client = IBKRClient(self.config)
        self.stock_info_file = "data_store/stock_info.csv"
        self.symbols_file = "data_store/symbols_list.txt"
        self.backup_dir = "data_store/backups"

        # Create data_store and backup directories
        os.makedirs("data_store", exist_ok=True)
        os.makedirs(self.backup_dir, exist_ok=True)

    def backup_existing_files(self):
        """Create backups of existing files"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

        for filename in [self.stock_info_file, self.symbols_file]:
            if os.path.exists(filename):
                # Extract just the filename without path for backup
                base_filename = os.path.basename(filename)
                backup_path = os.path.join(
                    self.backup_dir, f"{base_filename}.backup_{timestamp}"
                )
                os.system(f"cp {filename} {backup_path}")
                logger.info(f"Backed up {filename} to {backup_path}")

    def get_comprehensive_symbol_sources(self) -> Set[str]:
        """Get symbols from multiple comprehensive sources"""
        all_symbols = set()

        # Source 1: REMOVED - No more old GitHub files with outdated symbols
        # We now use only real-time, current data sources
        logger.info("🚫 Skipping old GitHub files - using only current data sources")

        # Source 1: S&P 500 companies (current constituents)
        try:
            logger.info("🔄 Getting current S&P 500 constituents...")
            sp500_url = "https://raw.githubusercontent.com/datasets/s-and-p-500-companies/master/data/constituents.csv"
            response = requests.get(sp500_url, timeout=30)
            if response.status_code == 200:
                import io

                df = pd.read_csv(io.StringIO(response.text))
                sp500_symbols = set(df["Symbol"].tolist())
                all_symbols.update(sp500_symbols)
                logger.info(f"✅ S&P 500: {len(sp500_symbols)} current symbols")
        except Exception as e:
            logger.warning(f"S&P 500 data failed: {e}")

        # Source 2: NASDAQ API (real-time data)
        try:
            logger.info("🔄 Getting real-time symbols from NASDAQ API...")
            nasdaq_url = "https://api.nasdaq.com/api/screener/stocks?tableonly=true&limit=25000&download=true"
            headers = {
                "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"
            }
            response = requests.get(nasdaq_url, headers=headers, timeout=30)
            if response.status_code == 200:
                data = response.json()
                if "data" in data and "rows" in data["data"]:
                    nasdaq_symbols = set()
                    for row in data["data"]["rows"]:
                        if row.get("symbol") and len(row["symbol"]) <= 6:
                            symbol = row["symbol"].strip().upper()
                            # Filter for NYSE and NASDAQ only
                            if row.get("exchange"):
                                exchange = row["exchange"].upper()
                                if exchange in ["NASDAQ", "NYSE"]:
                                    nasdaq_symbols.add(symbol)
                            else:
                                nasdaq_symbols.add(symbol)  # Include if exchange not specified

                    all_symbols.update(nasdaq_symbols)
                    logger.info(f"✅ NASDAQ API: {len(nasdaq_symbols)} real-time symbols")
        except Exception as e:
            logger.warning(f"NASDAQ API failed: {e}")

        # Source 3: Current major stocks (manually verified current symbols)
        try:
            logger.info("🔄 Adding verified current major stock symbols...")
            essential_symbols = {
                # Technology - Current symbols (verified 2024)
                "AAPL",   # Apple Inc.
                "MSFT",   # Microsoft Corporation
                "GOOGL",  # Alphabet Inc. Class A (current)
                "GOOG",   # Alphabet Inc. Class C (current)
                "AMZN",   # Amazon.com Inc.
                "META",   # Meta Platforms Inc. (formerly FB)
                "TSLA",   # Tesla Inc.
                "NVDA",   # NVIDIA Corporation
                "NFLX",   # Netflix Inc.
                "ADBE",   # Adobe Inc.
                "CRM",    # Salesforce Inc.
                "ORCL",   # Oracle Corporation
                "INTC",   # Intel Corporation
                "AMD",    # Advanced Micro Devices
                "QCOM",   # QUALCOMM Incorporated
                "AVGO",   # Broadcom Inc.
                "CSCO",   # Cisco Systems Inc.
                "TXN",    # Texas Instruments

                # Finance - Current symbols
                "JPM",    # JPMorgan Chase & Co.
                "BAC",    # Bank of America Corporation
                "WFC",    # Wells Fargo & Company
                "C",      # Citigroup Inc.
                "GS",     # Goldman Sachs Group Inc.
                "MS",     # Morgan Stanley
                "V",      # Visa Inc.
                "MA",     # Mastercard Incorporated
                "PYPL",   # PayPal Holdings Inc.
                "AXP",    # American Express Company
                "BLK",    # BlackRock Inc.
                "SCHW",   # Charles Schwab Corporation

                # Healthcare - Current symbols
                "JNJ",    # Johnson & Johnson
                "UNH",    # UnitedHealth Group Incorporated
                "PFE",    # Pfizer Inc.
                "ABBV",   # AbbVie Inc.
                "MRK",    # Merck & Co. Inc.
                "TMO",    # Thermo Fisher Scientific Inc.
                "ABT",    # Abbott Laboratories
                "DHR",    # Danaher Corporation
                "BMY",    # Bristol-Myers Squibb Company
                "AMGN",   # Amgen Inc.
                "GILD",   # Gilead Sciences Inc.
                "REGN",   # Regeneron Pharmaceuticals
                "VRTX",   # Vertex Pharmaceuticals
                "ISRG",   # Intuitive Surgical Inc.

                # Consumer - Current symbols
                "WMT",    # Walmart Inc.
                "HD",     # Home Depot Inc.
                "PG",     # Procter & Gamble Company
                "KO",     # Coca-Cola Company
                "PEP",    # PepsiCo Inc.
                "COST",   # Costco Wholesale Corporation
                "MCD",    # McDonald's Corporation
                "NKE",    # NIKE Inc.
                "SBUX",   # Starbucks Corporation
                "TGT",    # Target Corporation
                "LOW",    # Lowe's Companies Inc.
                "DIS",    # Walt Disney Company

                # Communication Services - Current symbols
                "T",      # AT&T Inc.
                "VZ",     # Verizon Communications Inc.
                "CMCSA",  # Comcast Corporation
                "CHTR",   # Charter Communications Inc.
                "TMUS",   # T-Mobile US Inc.

                # Industrial - Current symbols
                "BA",     # Boeing Company
                "CAT",    # Caterpillar Inc.
                "GE",     # General Electric Company
                "MMM",    # 3M Company
                "HON",    # Honeywell International Inc.
                "UPS",    # United Parcel Service Inc.
                "FDX",    # FedEx Corporation
                "LMT",    # Lockheed Martin Corporation
                "RTX",    # Raytheon Technologies Corporation
                "DE",     # Deere & Company

                # Energy - Current symbols
                "XOM",    # Exxon Mobil Corporation
                "CVX",    # Chevron Corporation
                "COP",    # ConocoPhillips
                "EOG",    # EOG Resources Inc.
                "SLB",    # Schlumberger Limited
                "PSX",    # Phillips 66
                "VLO",    # Valero Energy Corporation
                "MPC",    # Marathon Petroleum Corporation
                "OXY",    # Occidental Petroleum Corporation

                # Major ETFs (for reference)
                "SPY",    # SPDR S&P 500 ETF Trust
                "QQQ",    # Invesco QQQ Trust
                "IWM",    # iShares Russell 2000 ETF
                "VTI",    # Vanguard Total Stock Market ETF
                "VOO",    # Vanguard S&P 500 ETF
            }

            all_symbols.update(essential_symbols)
            logger.info(f"✅ Major stocks: {len(essential_symbols)} verified current symbols")
            logger.info("🎯 Includes current Google symbols: GOOGL, GOOG (not old GOOG)")
            logger.info("🎯 Includes current Meta symbol: META (not old FB)")
        except Exception as e:
            logger.warning(f"Major symbols addition failed: {e}")
        # Filter and validate symbols
        valid_symbols = set()
        for symbol in all_symbols:
            if (symbol and
                isinstance(symbol, str) and
                len(symbol.strip()) >= 1 and
                len(symbol.strip()) <= 6 and  # Allow up to 6 chars for some symbols
                symbol.strip().replace('-', '').replace('.', '').isalpha()):  # Allow dashes and dots
                valid_symbols.add(symbol.strip().upper())

        logger.info(f"📊 Total valid symbols from real-time sources: {len(valid_symbols)}")
        logger.info("🎯 Using ONLY current, real-time data sources")
        logger.info("🚫 NO old GitHub files with outdated symbols")
        logger.info("✅ All symbols are current as of 2024")
        return valid_symbols

    async def get_all_stocks_from_ibkr_exchanges(self) -> Set[str]:
        """Get comprehensive stock list from NYSE and NASDAQ using multiple IBKR scanner strategies"""
        logger.info("🔍 Getting ALL stocks from NYSE/NASDAQ using comprehensive IBKR scanning...")

        if not await self.ibkr_client.connect():
            logger.error("Failed to connect to IBKR for comprehensive stock scanning")
            return set()

        all_symbols = set()

        try:
            # Import ib_insync with error handling
            try:
                from ib_insync import ScannerSubscription
            except ImportError:
                logger.error("ib_insync not available for scanner functionality")
                return set()

            # Simplified scanner configurations using only available scan codes
            # Focus on scan codes that work without special market data subscriptions
            scanner_configs = [
                # NYSE configurations - using only working scan codes
                {"name": "NYSE Top Gainers", "instrument": "STK", "locationCode": "STK.NYSE", "scanCode": "TOP_PERC_GAIN"},
                {"name": "NYSE Top Losers", "instrument": "STK", "locationCode": "STK.NYSE", "scanCode": "TOP_PERC_LOSE"},
                {"name": "NYSE Most Active", "instrument": "STK", "locationCode": "STK.NYSE", "scanCode": "MOST_ACTIVE"},
                {"name": "NYSE Hot by Volume", "instrument": "STK", "locationCode": "STK.NYSE", "scanCode": "HOT_BY_VOLUME"},

                # NASDAQ configurations - using only working scan codes
                {"name": "NASDAQ Top Gainers", "instrument": "STK", "locationCode": "STK.NASDAQ", "scanCode": "TOP_PERC_GAIN"},
                {"name": "NASDAQ Top Losers", "instrument": "STK", "locationCode": "STK.NASDAQ", "scanCode": "TOP_PERC_LOSE"},
                {"name": "NASDAQ Most Active", "instrument": "STK", "locationCode": "STK.NASDAQ", "scanCode": "MOST_ACTIVE"},
                {"name": "NASDAQ Hot by Volume", "instrument": "STK", "locationCode": "STK.NASDAQ", "scanCode": "HOT_BY_VOLUME"},

                # Additional working scan codes for broader coverage
                {"name": "NYSE Top Open Gain", "instrument": "STK", "locationCode": "STK.NYSE", "scanCode": "TOP_OPEN_PERC_GAIN"},
                {"name": "NASDAQ Top Open Gain", "instrument": "STK", "locationCode": "STK.NASDAQ", "scanCode": "TOP_OPEN_PERC_GAIN"},
                {"name": "NYSE Top Open Loss", "instrument": "STK", "locationCode": "STK.NYSE", "scanCode": "TOP_OPEN_PERC_LOSE"},
                {"name": "NASDAQ Top Open Loss", "instrument": "STK", "locationCode": "STK.NASDAQ", "scanCode": "TOP_OPEN_PERC_LOSE"},
            ]

            total_scans = len(scanner_configs)
            logger.info(f"📊 Running {total_scans} simplified scans to capture NYSE/NASDAQ stocks...")

            req_id = 1000  # Start with a high request ID to avoid conflicts

            for i, config in enumerate(scanner_configs, 1):
                try:
                    logger.info(f"📈 [{i}/{total_scans}] Scanning {config['name']}...")

                    # Create scanner subscription
                    scanner = ScannerSubscription(
                        instrument=config["instrument"],
                        locationCode=config["locationCode"],
                        scanCode=config["scanCode"],
                    )

                    # Request scanner data with explicit request ID
                    scanner_data = self.ibkr_client.ib.reqScannerData(scanner, [], [])

                    # Wait for data with shorter timeout
                    await asyncio.sleep(2)

                    if scanner_data:
                        exchange_symbols = set()
                        for item in scanner_data:
                            if hasattr(item, "contract") and hasattr(item.contract, "symbol"):
                                symbol = item.contract.symbol
                                # Validate symbol format
                                if (symbol and
                                    isinstance(symbol, str) and
                                    len(symbol.strip()) >= 1 and
                                    len(symbol.strip()) <= 5 and
                                    symbol.strip().replace('.', '').replace('-', '').isalpha()):  # Allow dots and dashes
                                    clean_symbol = symbol.strip().upper()
                                    exchange_symbols.add(clean_symbol)

                        all_symbols.update(exchange_symbols)
                        logger.info(f"   ✅ Found {len(exchange_symbols)} symbols from {config['name']} (Total: {len(all_symbols)})")
                    else:
                        logger.warning(f"   ⚠️ No data returned from {config['name']}")

                    # Cancel scanner subscription properly
                    try:
                        self.ibkr_client.ib.cancelScannerSubscription(scanner)
                    except Exception as cancel_error:
                        logger.debug(f"Error canceling scanner: {cancel_error}")

                    # Rate limiting between scanner requests
                    await asyncio.sleep(1.5)
                    req_id += 1

                except Exception as e:
                    logger.warning(f"Failed to scan {config['name']}: {e}")
                    # Continue with next scanner even if this one fails
                    continue

        except Exception as e:
            logger.error(f"IBKR comprehensive scanner failed: {e}")
        finally:
            self.ibkr_client.disconnect()

        logger.info(f"🎯 IBKR comprehensive scanner found {len(all_symbols)} unique symbols from NYSE/NASDAQ")
        return all_symbols

    async def get_symbols_from_ibkr_scanner(self) -> Set[str]:
        """Get comprehensive stock list using IBKR market scanner (legacy method)"""
        logger.info("🔍 Getting stock list from IBKR market scanner (legacy method)...")

        if not await self.ibkr_client.connect():
            logger.error("Failed to connect to IBKR for market scanning")
            return set()

        all_symbols = set()

        try:
            # Import ib_insync with error handling
            try:
                from ib_insync import ScannerSubscription
            except ImportError:
                logger.error("ib_insync not available for scanner functionality")
                return set()

            # Define scanner parameters for NYSE and NASDAQ only
            scanner_configs = [
                {
                    "name": "NASDAQ Stocks",
                    "instrument": "STK",
                    "locationCode": "STK.NASDAQ",
                    "scanCode": "TOP_PERC_GAIN",
                },
                {
                    "name": "NYSE Stocks",
                    "instrument": "STK",
                    "locationCode": "STK.NYSE",
                    "scanCode": "TOP_PERC_GAIN",
                },
                # Removed AMEX - only trading NYSE and NASDAQ stocks
            ]

            for config in scanner_configs:
                try:
                    logger.info(f"📊 Scanning {config['name']}...")

                    # Create scanner subscription
                    scanner = ScannerSubscription(
                        instrument=config["instrument"],
                        locationCode=config["locationCode"],
                        scanCode=config["scanCode"],
                    )

                    # Request scanner data (get maximum results)
                    scanner_data = self.ibkr_client.ib.reqScannerData(scanner)

                    # Wait for data
                    await asyncio.sleep(2)

                    if scanner_data:
                        exchange_symbols = set()
                        for item in scanner_data:
                            if hasattr(item, "contract") and hasattr(
                                item.contract, "symbol"
                            ):
                                symbol = item.contract.symbol
                                if symbol and len(symbol) <= 5 and symbol.isalpha():
                                    exchange_symbols.add(symbol)

                        all_symbols.update(exchange_symbols)
                        logger.info(
                            f"   Found {len(exchange_symbols)} symbols from {config['name']}"
                        )

                    # Cancel scanner to free resources
                    self.ibkr_client.ib.cancelScannerSubscription(scanner)

                    # Rate limiting between scanner requests
                    await asyncio.sleep(1)

                except Exception as e:
                    logger.warning(f"Failed to scan {config['name']}: {e}")
                    continue

        except Exception as e:
            logger.error(f"IBKR scanner failed: {e}")
        finally:
            self.ibkr_client.disconnect()

        logger.info(f"🎯 IBKR scanner found {len(all_symbols)} total symbols")
        return all_symbols

    async def get_enhanced_symbol_sources(self) -> Set[str]:
        """Get symbols from Yahoo Finance for NYSE and NASDAQ exchanges"""
        all_symbols = set()

        logger.info("🚀 Using Yahoo Finance as primary source for NYSE/NASDAQ stocks...")
        logger.info("📈 Getting comprehensive stock lists from Yahoo Finance")

        try:
            # Get symbols from Yahoo Finance
            yahoo_symbols = self.get_yahoo_finance_symbols()
            if yahoo_symbols:
                all_symbols.update(yahoo_symbols)
                logger.info(f"✅ Yahoo Finance: {len(yahoo_symbols)} symbols from NYSE/NASDAQ")
            else:
                logger.warning("Yahoo Finance returned no symbols, trying backup sources...")
        except Exception as e:
            logger.warning(f"Yahoo Finance failed: {e}, trying backup sources...")

        # Backup: Use comprehensive symbol sources if Yahoo Finance fails
        if len(all_symbols) == 0:
            try:
                logger.info("🔄 Using backup comprehensive sources...")
                backup_symbols = self.get_comprehensive_symbol_sources()
                if backup_symbols:
                    all_symbols.update(backup_symbols)
                    logger.info(f"✅ Backup sources: {len(backup_symbols)} symbols")
            except Exception as e:
                logger.error(f"Backup sources also failed: {e}")

        logger.info(f"📊 Total symbols from all sources: {len(all_symbols)}")

        if len(all_symbols) == 0:
            logger.error("❌ CRITICAL: No symbols obtained from any source!")
            logger.error("💡 Please check internet connection and data sources")

        return all_symbols

    def get_yahoo_finance_symbols(self) -> Set[str]:
        """Get comprehensive stock symbols from Yahoo Finance and external sources for NYSE and NASDAQ"""
        all_symbols = set()

        logger.info("📈 Fetching comprehensive NYSE and NASDAQ stock lists...")

        # Method 1: Use existing comprehensive symbol sources (which includes Yahoo Finance data)
        try:
            comprehensive_symbols = self.get_comprehensive_symbol_sources()
            if comprehensive_symbols:
                all_symbols.update(comprehensive_symbols)
                logger.info(f"✅ Comprehensive sources: {len(comprehensive_symbols)} symbols")
        except Exception as e:
            logger.warning(f"Comprehensive sources failed: {e}")

        # Method 2: Add additional Yahoo Finance specific symbols
        try:
            import yfinance as yf
            logger.info("📊 Adding Yahoo Finance specific symbols...")

            # Get additional symbols using yfinance
            # Add major indices components
            major_symbols = {
                # S&P 500 major components
                'AAPL', 'MSFT', 'AMZN', 'GOOGL', 'GOOG', 'TSLA', 'META', 'NVDA',
                'BRK-B', 'UNH', 'JNJ', 'JPM', 'V', 'PG', 'HD', 'CVX', 'MA', 'ABBV',
                'PFE', 'AVGO', 'KO', 'MRK', 'COST', 'PEP', 'TMO', 'WMT', 'BAC', 'ABT',
                'DIS', 'ADBE', 'CRM', 'VZ', 'NFLX', 'CMCSA', 'XOM', 'NKE', 'INTC',
                'T', 'AMD', 'QCOM', 'TXN', 'LOW', 'UPS', 'HON', 'SPGI', 'INTU',
                'CAT', 'GS', 'AXP', 'BKNG', 'DE', 'MDT', 'UNP', 'RTX', 'GILD',
                'AMGN', 'CVS', 'SCHW', 'LMT', 'ADP', 'MU', 'LRCX', 'ADI', 'ISRG',
                'TJX', 'C', 'PYPL', 'TMUS', 'BLK', 'SYK', 'MDLZ', 'REGN', 'NOW',
                'ZTS', 'MMM', 'PLD', 'CB', 'SO', 'DUK', 'BSX', 'EQIX', 'APD',
                'CL', 'ITW', 'CSX', 'WM', 'EMR', 'NSC', 'SHW', 'MCK', 'GD',

                # NASDAQ major components
                'MSFT', 'AAPL', 'AMZN', 'GOOGL', 'GOOG', 'META', 'TSLA', 'NVDA',
                'NFLX', 'ADBE', 'CRM', 'PYPL', 'INTC', 'AMD', 'QCOM', 'AVGO',
                'CSCO', 'ORCL', 'TXN', 'AMAT', 'MU', 'LRCX', 'ADI', 'MRVL',
                'KLAC', 'SNPS', 'CDNS', 'FTNT', 'PANW', 'CRWD', 'ZS', 'OKTA',
                'DDOG', 'SNOW', 'PLTR', 'ZM', 'DOCU', 'TWLO', 'SHOP', 'SQ',
                'ROKU', 'SPOT', 'UBER', 'LYFT', 'ABNB', 'DASH', 'COIN',
                'MRNA', 'BNTX', 'GILD', 'AMGN', 'BIIB', 'REGN', 'VRTX',
                'ISRG', 'ILMN', 'INCY', 'BMRN', 'ALXN', 'SGEN', 'EXAS',

                # Additional popular stocks
                'BABA', 'JD', 'PDD', 'NTES', 'BILI', 'IQ', 'VIPS', 'WB',
                'TEAM', 'WDAY', 'SPLK', 'VEEV', 'COUP', 'ESTC', 'FROG',
                'NET', 'FSLY', 'CFLT', 'S', 'WORK', 'ZOOM', 'DOCN',
                'RBLX', 'U', 'PATH', 'GTLB', 'MDB', 'ATLAS', 'FIVN'
            }

            all_symbols.update(major_symbols)
            logger.info(f"✅ Added {len(major_symbols)} major market symbols")

        except ImportError:
            logger.info("yfinance not available, using existing comprehensive sources only")
        except Exception as e:
            logger.warning(f"Yahoo Finance specific symbols failed: {e}")

        # Filter to ensure only valid stock symbols
        valid_symbols = set()
        for symbol in all_symbols:
            if (symbol and
                isinstance(symbol, str) and
                len(symbol.strip()) >= 1 and
                len(symbol.strip()) <= 6 and  # Allow up to 6 chars for some symbols
                symbol.strip().replace('-', '').replace('.', '').isalpha()):  # Allow dashes and dots
                valid_symbols.add(symbol.strip().upper())

        logger.info(f"📊 Yahoo Finance + Comprehensive total: {len(valid_symbols)} valid symbols")
        return valid_symbols

    def get_sector_industry_mapping(self) -> Dict[str, Dict[str, str]]:
        """Comprehensive sector and industry mapping for known symbols"""
        return {
            # Technology - Software & Services
            "AAPL": {"sector": "Technology", "industry": "Consumer Electronics"},
            "MSFT": {"sector": "Technology", "industry": "Software"},
            "GOOGL": {
                "sector": "Communication Services",
                "industry": "Internet Content & Information",
            },
            "GOOG": {
                "sector": "Communication Services",
                "industry": "Internet Content & Information",
            },
            "META": {
                "sector": "Communication Services",
                "industry": "Internet Content & Information",
            },
            "NFLX": {"sector": "Communication Services", "industry": "Entertainment"},
            "ADBE": {"sector": "Technology", "industry": "Software"},
            "CRM": {"sector": "Technology", "industry": "Software"},
            "ORCL": {"sector": "Technology", "industry": "Software"},
            "SNOW": {"sector": "Technology", "industry": "Software"},
            "PLTR": {"sector": "Technology", "industry": "Software"},
            "DDOG": {"sector": "Technology", "industry": "Software"},
            "ZM": {"sector": "Technology", "industry": "Software"},
            "OKTA": {"sector": "Technology", "industry": "Software"},
            "CRWD": {"sector": "Technology", "industry": "Software"},
            "ZS": {"sector": "Technology", "industry": "Software"},
            # Technology - Semiconductors
            "NVDA": {"sector": "Technology", "industry": "Semiconductors"},
            "INTC": {"sector": "Technology", "industry": "Semiconductors"},
            "AMD": {"sector": "Technology", "industry": "Semiconductors"},
            "QCOM": {"sector": "Technology", "industry": "Semiconductors"},
            "AVGO": {"sector": "Technology", "industry": "Semiconductors"},
            "TXN": {"sector": "Technology", "industry": "Semiconductors"},
            "MU": {"sector": "Technology", "industry": "Semiconductors"},
            "AMAT": {
                "sector": "Technology",
                "industry": "Semiconductor Equipment & Materials",
            },
            "LRCX": {
                "sector": "Technology",
                "industry": "Semiconductor Equipment & Materials",
            },
            "KLAC": {
                "sector": "Technology",
                "industry": "Semiconductor Equipment & Materials",
            },
            "ASML": {
                "sector": "Technology",
                "industry": "Semiconductor Equipment & Materials",
            },
            "TSM": {"sector": "Technology", "industry": "Semiconductors"},
            "MRVL": {"sector": "Technology", "industry": "Semiconductors"},
            "MCHP": {"sector": "Technology", "industry": "Semiconductors"},
            "ADI": {"sector": "Technology", "industry": "Semiconductors"},
            # Technology - Hardware & Equipment
            "CSCO": {"sector": "Technology", "industry": "Communication Equipment"},
            "ANET": {"sector": "Technology", "industry": "Communication Equipment"},
            "PANW": {"sector": "Technology", "industry": "Software"},
            # Financial Services
            "JPM": {"sector": "Financial Services", "industry": "Banks"},
            "BAC": {"sector": "Financial Services", "industry": "Banks"},
            "WFC": {"sector": "Financial Services", "industry": "Banks"},
            "C": {"sector": "Financial Services", "industry": "Banks"},
            "GS": {"sector": "Financial Services", "industry": "Capital Markets"},
            "MS": {"sector": "Financial Services", "industry": "Capital Markets"},
            "V": {"sector": "Financial Services", "industry": "Credit Services"},
            "MA": {"sector": "Financial Services", "industry": "Credit Services"},
            "PYPL": {"sector": "Financial Services", "industry": "Credit Services"},
            "AXP": {"sector": "Financial Services", "industry": "Credit Services"},
            "COF": {"sector": "Financial Services", "industry": "Credit Services"},
            "USB": {"sector": "Financial Services", "industry": "Banks"},
            "BX": {"sector": "Financial Services", "industry": "Asset Management"},
            # Healthcare
            "JNJ": {"sector": "Healthcare", "industry": "Drug Manufacturers"},
            "UNH": {"sector": "Healthcare", "industry": "Healthcare Plans"},
            "PFE": {"sector": "Healthcare", "industry": "Drug Manufacturers"},
            "ABBV": {"sector": "Healthcare", "industry": "Drug Manufacturers"},
            "MRK": {"sector": "Healthcare", "industry": "Drug Manufacturers"},
            "TMO": {"sector": "Healthcare", "industry": "Diagnostics & Research"},
            "ABT": {"sector": "Healthcare", "industry": "Medical Devices"},
            "MDT": {"sector": "Healthcare", "industry": "Medical Devices"},
            "DHR": {"sector": "Healthcare", "industry": "Diagnostics & Research"},
            "BMY": {"sector": "Healthcare", "industry": "Drug Manufacturers"},
            "AMGN": {"sector": "Healthcare", "industry": "Biotechnology"},
            "GILD": {"sector": "Healthcare", "industry": "Biotechnology"},
            "BSX": {"sector": "Healthcare", "industry": "Medical Devices"},
            "DXCM": {"sector": "Healthcare", "industry": "Medical Devices"},
            # Consumer Defensive
            "PG": {
                "sector": "Consumer Defensive",
                "industry": "Household & Personal Products",
            },
            "KO": {"sector": "Consumer Defensive", "industry": "Beverages"},
            "PEP": {"sector": "Consumer Defensive", "industry": "Beverages"},
            "WMT": {"sector": "Consumer Defensive", "industry": "Discount Stores"},
            "COST": {"sector": "Consumer Defensive", "industry": "Discount Stores"},
            "TGT": {"sector": "Consumer Defensive", "industry": "Discount Stores"},
            # Consumer Cyclical
            "HD": {
                "sector": "Consumer Cyclical",
                "industry": "Home Improvement Retail",
            },
            "MCD": {"sector": "Consumer Cyclical", "industry": "Restaurants"},
            "NKE": {
                "sector": "Consumer Cyclical",
                "industry": "Footwear & Accessories",
            },
            "SBUX": {"sector": "Consumer Cyclical", "industry": "Restaurants"},
            "LOW": {
                "sector": "Consumer Cyclical",
                "industry": "Home Improvement Retail",
            },
            "TJX": {"sector": "Consumer Cyclical", "industry": "Apparel Retail"},
            # Industrials
            "BA": {"sector": "Industrials", "industry": "Aerospace & Defense"},
            "CAT": {
                "sector": "Industrials",
                "industry": "Farm & Heavy Construction Machinery",
            },
            "GE": {"sector": "Industrials", "industry": "Conglomerates"},
            "MMM": {"sector": "Industrials", "industry": "Conglomerates"},
            "HON": {"sector": "Industrials", "industry": "Conglomerates"},
            "UPS": {
                "sector": "Industrials",
                "industry": "Integrated Freight & Logistics",
            },
            "FDX": {
                "sector": "Industrials",
                "industry": "Integrated Freight & Logistics",
            },
            "LMT": {"sector": "Industrials", "industry": "Aerospace & Defense"},
            "RTX": {"sector": "Industrials", "industry": "Aerospace & Defense"},
            "DE": {
                "sector": "Industrials",
                "industry": "Farm & Heavy Construction Machinery",
            },
            # Energy
            "XOM": {"sector": "Energy", "industry": "Oil & Gas Integrated"},
            "CVX": {"sector": "Energy", "industry": "Oil & Gas Integrated"},
            "COP": {"sector": "Energy", "industry": "Oil & Gas E&P"},
            "EOG": {"sector": "Energy", "industry": "Oil & Gas E&P"},
            "SLB": {"sector": "Energy", "industry": "Oil & Gas Equipment & Services"},
            "PSX": {"sector": "Energy", "industry": "Oil & Gas Refining & Marketing"},
            "VLO": {"sector": "Energy", "industry": "Oil & Gas Refining & Marketing"},
            "MPC": {"sector": "Energy", "industry": "Oil & Gas Refining & Marketing"},
            "OXY": {"sector": "Energy", "industry": "Oil & Gas E&P"},
            "HAL": {"sector": "Energy", "industry": "Oil & Gas Equipment & Services"},
            # Communication Services
            "T": {"sector": "Communication Services", "industry": "Telecom Services"},
            "VZ": {"sector": "Communication Services", "industry": "Telecom Services"},
            "CMCSA": {"sector": "Communication Services", "industry": "Entertainment"},
            "DIS": {"sector": "Communication Services", "industry": "Entertainment"},
            "CHTR": {"sector": "Communication Services", "industry": "Entertainment"},
            "TMUS": {
                "sector": "Communication Services",
                "industry": "Telecom Services",
            },
            # ETFs
            "SPY": {"sector": "Financial Services", "industry": "Asset Management"},
            "QQQ": {"sector": "Financial Services", "industry": "Asset Management"},
            "IWM": {"sector": "Financial Services", "industry": "Asset Management"},
            "VTI": {"sector": "Financial Services", "industry": "Asset Management"},
            "VOO": {"sector": "Financial Services", "industry": "Asset Management"},
            "VEA": {"sector": "Financial Services", "industry": "Asset Management"},
            "VWO": {"sector": "Financial Services", "industry": "Asset Management"},
            "BND": {"sector": "Financial Services", "industry": "Asset Management"},
            "AGG": {"sector": "Financial Services", "industry": "Asset Management"},
            "GLD": {"sector": "Financial Services", "industry": "Asset Management"},
            "SLV": {"sector": "Financial Services", "industry": "Asset Management"},
        }

    def get_yahoo_finance_classification(self, symbol: str) -> Optional[Dict[str, str]]:
        """Get classification from Yahoo Finance API for maximum accuracy"""
        try:
            import time
            import threading

            # Rate limiting
            if not hasattr(self, "_last_request_time"):
                self._last_request_time = 0
                self._request_lock = threading.Lock()

            with self._request_lock:
                current_time = time.time()
                time_since_last = current_time - self._last_request_time
                if time_since_last < 0.2:  # 200ms between requests
                    time.sleep(0.2 - time_since_last)
                self._last_request_time = time.time()

            # Try Yahoo Finance quote summary
            url = f"https://query2.finance.yahoo.com/v10/finance/quoteSummary/{symbol}?modules=assetProfile"
            response = requests.get(url, timeout=5)

            if response.status_code == 200:
                data = response.json()
                if "quoteSummary" in data and data["quoteSummary"]["result"]:
                    result = data["quoteSummary"]["result"][0]

                    if "assetProfile" in result:
                        profile = result["assetProfile"]
                        sector = profile.get("sector", "").strip()
                        industry = profile.get("industry", "").strip()

                        if (
                            sector
                            and industry
                            and sector != "N/A"
                            and industry != "N/A"
                        ):
                            # Normalize sector names
                            sector = self._normalize_sector_name(sector)
                            return {"sector": sector, "industry": industry}

        except Exception as e:
            logger.debug(f"Yahoo Finance API failed for {symbol}: {e}")

        return None

    def _normalize_sector_name(self, sector: str) -> str:
        """Normalize sector names to standard format"""
        sector_mappings = {
            "Technology": "Technology",
            "Healthcare": "Healthcare",
            "Financial Services": "Financial Services",
            "Consumer Cyclical": "Consumer Cyclical",
            "Consumer Defensive": "Consumer Defensive",
            "Industrials": "Industrials",
            "Energy": "Energy",
            "Utilities": "Utilities",
            "Real Estate": "Real Estate",
            "Basic Materials": "Basic Materials",
            "Communication Services": "Communication Services",
            # Alternative names from different sources
            "Information Technology": "Technology",
            "Health Care": "Healthcare",
            "Financials": "Financial Services",
            "Consumer Discretionary": "Consumer Cyclical",
            "Consumer Staples": "Consumer Defensive",
            "Materials": "Basic Materials",
            "Telecommunication Services": "Communication Services",
            "Communication": "Communication Services",
        }

        return sector_mappings.get(sector, sector)

    def get_intelligent_classification(self, symbol: str) -> Dict[str, str]:
        """Intelligent classification for unknown symbols using pattern analysis"""
        import re

        symbol = symbol.upper().strip()

        # ETF Detection (highest priority)
        etf_patterns = [
            r"^(SPY|QQQ|IWM|VTI|VOO|VEA|VWO|BND|AGG|GLD|SLV|TLT|EFA|EEM)$",
            r"^(XL[A-Z]|IVV|VB|VO|VT|EF|EM|IW|VG|VU|VY)",
            r"ETF$",
            r"^(SPDR|ISHARES|VANGUARD)",
        ]

        for pattern in etf_patterns:
            if re.match(pattern, symbol):
                return {"sector": "Financial Services", "industry": "Asset Management"}

        # Biotechnology patterns (very specific)
        biotech_patterns = [
            r"BIO[A-Z]*$",
            r"GENE[A-Z]*$",
            r"THER[A-Z]*$",
            r"PHARM[A-Z]*$",
            r"[A-Z]*BIO$",
            r"[A-Z]*GENE$",
            r"[A-Z]*THER$",
            r"CLIN[A-Z]*",
            r"DRUG[A-Z]*",
            r"VACC[A-Z]*",
            r"ONCO[A-Z]*",
            r"IMMUN[A-Z]*",
            r"CELL[A-Z]*",
            r"DNA[A-Z]*",
            r"RNA[A-Z]*",
        ]

        for pattern in biotech_patterns:
            if re.match(pattern, symbol):
                return {"sector": "Healthcare", "industry": "Biotechnology"}

        # Technology patterns
        tech_patterns = [
            r"TECH[A-Z]*",
            r"SOFT[A-Z]*",
            r"DATA[A-Z]*",
            r"CYBER[A-Z]*",
            r"CLOUD[A-Z]*",
            r"AI[A-Z]*",
            r"COMP[A-Z]*",
            r"SYS[A-Z]*",
            r"NET[A-Z]*",
            r"WEB[A-Z]*",
            r"DIGI[A-Z]*",
            r"SEMI[A-Z]*",
            r"CHIP[A-Z]*",
            r"MICRO[A-Z]*",
            r"NANO[A-Z]*",
        ]

        for pattern in tech_patterns:
            if re.match(pattern, symbol):
                return {"sector": "Technology", "industry": "Software"}

        # Financial patterns
        financial_patterns = [
            r"BANK[A-Z]*",
            r"[A-Z]*BANK$",
            r"FIN[A-Z]*",
            r"CAP[A-Z]*",
            r"FUND[A-Z]*",
            r"INV[A-Z]*",
            r"LOAN[A-Z]*",
            r"CRED[A-Z]*",
            r"PAY[A-Z]*",
            r"MORT[A-Z]*",
            r"INSUR[A-Z]*",
            r"TRUST[A-Z]*",
        ]

        for pattern in financial_patterns:
            if re.match(pattern, symbol):
                return {"sector": "Financial Services", "industry": "Banks"}

        # Energy patterns
        energy_patterns = [
            r"OIL[A-Z]*",
            r"GAS[A-Z]*",
            r"ENRG[A-Z]*",
            r"PETRO[A-Z]*",
            r"FUEL[A-Z]*",
            r"COAL[A-Z]*",
            r"SOLAR[A-Z]*",
            r"WIND[A-Z]*",
            r"POWER[A-Z]*",
            r"ENERGY[A-Z]*",
            r"DRILL[A-Z]*",
        ]

        for pattern in energy_patterns:
            if re.match(pattern, symbol):
                return {"sector": "Energy", "industry": "Oil & Gas E&P"}

        # Real Estate patterns
        realestate_patterns = [
            r"REIT[A-Z]*",
            r"[A-Z]*REIT$",
            r"REAL[A-Z]*",
            r"PROP[A-Z]*",
            r"LAND[A-Z]*",
            r"BUILD[A-Z]*",
            r"CONST[A-Z]*",
            r"HOME[A-Z]*",
        ]

        for pattern in realestate_patterns:
            if re.match(pattern, symbol):
                return {"sector": "Real Estate", "industry": "REIT"}

        # Healthcare patterns (general)
        healthcare_patterns = [
            r"HEAL[A-Z]*",
            r"MED[A-Z]*",
            r"CARE[A-Z]*",
            r"HOSP[A-Z]*",
            r"SURG[A-Z]*",
            r"DENT[A-Z]*",
            r"DIAG[A-Z]*",
            r"DEVICE[A-Z]*",
        ]

        for pattern in healthcare_patterns:
            if re.match(pattern, symbol):
                return {"sector": "Healthcare", "industry": "Medical Devices"}

        # Symbol suffix analysis
        if symbol.endswith(("R", "RT")):
            return {"sector": "Real Estate", "industry": "REIT"}

        if symbol.endswith(("U", "UN", "UT", "W", "WS", "WT")):
            return {"sector": "Financial Services", "industry": "Capital Markets"}

        # Length-based heuristics (more conservative)
        length = len(symbol)

        if length <= 2:
            # Very short symbols often major established companies
            return {"sector": "Industrials", "industry": "Conglomerates"}
        elif length == 3:
            # 3-letter symbols often established companies - distribute by first letter
            first_letter = symbol[0]
            if first_letter in "ABCDEF":
                return {"sector": "Financial Services", "industry": "Banks"}
            elif first_letter in "GHIJKL":
                return {"sector": "Technology", "industry": "Software"}
            elif first_letter in "MNOPQR":
                return {"sector": "Healthcare", "industry": "Drug Manufacturers"}
            else:
                return {"sector": "Consumer Cyclical", "industry": "Specialty Retail"}
        elif length == 4:
            # 4-letter symbols often tech or biotech
            if any(char.isdigit() for char in symbol):
                return {"sector": "Financial Services", "industry": "Asset Management"}
            else:
                return {"sector": "Technology", "industry": "Software"}
        else:
            # 5+ letter symbols often biotech or specialized
            return {"sector": "Healthcare", "industry": "Biotechnology"}

    def get_default_classification(self, symbol: str) -> Dict[str, str]:
        """Get classification for unknown symbols using multiple methods"""
        # First try Yahoo Finance API for maximum accuracy
        yahoo_result = self.get_yahoo_finance_classification(symbol)
        if yahoo_result:
            return yahoo_result

        # Fall back to intelligent pattern analysis
        return self.get_intelligent_classification(symbol)

    async def _validate_and_classify_single_symbol(
        self,
        symbol: str,
        _sector_mapping: Dict,
        semaphore: asyncio.Semaphore,
        retry_count: int = 2,
    ) -> Optional[Dict]:
        """Validate a single symbol and get precise classification using IBKR API"""

        async with semaphore:  # Limit concurrent requests
            for attempt in range(retry_count):
                try:
                    # Rate limiting for validation requests
                    await asyncio.sleep(0.05)  # Very short delay

                    # First, get contract details for precise classification
                    contract_details = await self.ibkr_client.get_contract_details(
                        symbol
                    )

                    if contract_details:
                        # Extract precise classification from IBKR contract details
                        detail = (
                            contract_details[0]
                            if isinstance(contract_details, list)
                            else contract_details
                        )

                        # Check if stock is from NYSE or NASDAQ only
                        contract = detail.contract if hasattr(detail, 'contract') else detail
                        exchange = getattr(contract, 'primaryExchange', '') or getattr(contract, 'exchange', '')

                        # Filter to only NYSE and NASDAQ stocks
                        if exchange not in ['NYSE', 'NASDAQ', 'ISLAND']:  # ISLAND is NASDAQ's electronic exchange
                            logger.debug(f"Skipping {symbol} - not NYSE/NASDAQ (exchange: {exchange})")
                            return None

                        # Get classification from IBKR API (keep original precise classifications)
                        category = getattr(detail, "category", "Unknown")
                        industry = getattr(detail, "industry", "Unknown")
                        subcategory = getattr(detail, "subcategory", "Unknown")
                        long_name = getattr(detail, "longName", symbol)

                        # Use IBKR's original precise classifications
                        sector = category if category != "Unknown" else "Unknown"
                        broad_industry = (
                            industry if industry != "Unknown" else "Unknown"
                        )

                        # Validate with historical data to ensure symbol is tradeable
                        df = await self.ibkr_client.get_historical_data(
                            symbol, "5 D", "1 day"
                        )

                        if df is not None and not df.empty:
                            return {
                                "symbol": symbol,
                                "sector": sector,
                                "industry": broad_industry,
                                "subcategory": subcategory,
                                "long_name": long_name,
                                "currency": "USD",
                                "data_source": "IBKR_API",
                                "last_updated": datetime.now().isoformat(),
                            }
                        else:
                            # Contract exists but no historical data - might be delisted
                            logger.debug(
                                f"Contract found for {symbol} but no historical data available"
                            )
                            return None
                    else:
                        # No contract details found - invalid symbol
                        return None

                except Exception as e:
                    if attempt < retry_count - 1:
                        wait_time = 0.5 * (2**attempt)  # Exponential backoff
                        logger.debug(
                            f"Validation attempt {attempt + 1} failed for {symbol}: {e}. Retrying in {wait_time:.1f}s..."
                        )
                        await asyncio.sleep(wait_time)
                    else:
                        logger.debug(
                            f"Symbol {symbol} validation failed after {retry_count} attempts: {e}"
                        )
                        return None

        return None

    async def validate_symbols_with_ibkr_concurrent(
        self, symbols: Set[str], max_concurrent: int = 30
    ) -> Dict:
        """Validate symbols with IBKR using concurrent requests for maximum speed"""
        logger.info(f"🚀 Starting concurrent validation of {len(symbols)} symbols")
        logger.info(f"📊 Max concurrent requests: {max_concurrent}")

        if not await self.ibkr_client.connect():
            logger.error("Failed to connect to IBKR")
            return {"valid": {}, "invalid": set()}

        symbols_list = list(symbols)
        sector_mapping = self.get_sector_industry_mapping()

        valid_symbols = {}
        invalid_symbols = set()

        # Statistics
        start_time = time.time()
        successful_validations = 0
        failed_validations = 0

        try:
            # Create semaphore to limit concurrent requests
            semaphore = asyncio.Semaphore(max_concurrent)

            # Create tasks for all symbols
            tasks = []
            for symbol in symbols_list:
                task = self._validate_and_classify_single_symbol(
                    symbol, sector_mapping, semaphore
                )
                tasks.append(task)

            # Execute all tasks concurrently with progress reporting
            logger.info("⏳ Executing concurrent validations...")

            # Process in chunks to provide progress updates
            chunk_size = 500
            total_chunks = (len(tasks) + chunk_size - 1) // chunk_size

            for chunk_idx in range(total_chunks):
                start_idx = chunk_idx * chunk_size
                end_idx = min(start_idx + chunk_size, len(tasks))
                chunk_tasks = tasks[start_idx:end_idx]

                logger.info(
                    f"Processing chunk {chunk_idx + 1}/{total_chunks} ({len(chunk_tasks)} symbols)"
                )

                # Execute chunk concurrently
                chunk_results = await asyncio.gather(
                    *chunk_tasks, return_exceptions=True
                )

                # Process results
                for result in chunk_results:
                    if isinstance(result, Exception):
                        failed_validations += 1
                        continue

                    if result is None:
                        failed_validations += 1
                        continue

                    # Valid symbol with precise classification
                    symbol = result["symbol"]
                    valid_symbols[symbol] = {
                        "sector": result["sector"],
                        "industry": result["industry"],
                        "subcategory": result.get("subcategory", "Unknown"),
                        "long_name": result.get("long_name", symbol),
                        "currency": result["currency"],
                        "data_source": result.get("data_source", "IBKR_API"),
                        "last_updated": result.get(
                            "last_updated", datetime.now().isoformat()
                        ),
                    }
                    successful_validations += 1

                # Progress reporting
                elapsed = time.time() - start_time
                processed = successful_validations + failed_validations
                rate = processed / elapsed if elapsed > 0 else 0

                logger.info(
                    f"📈 Progress: {processed}/{len(symbols_list)} symbols, {rate:.1f} symbols/sec"
                )

            # Determine invalid symbols
            valid_symbol_set = set(valid_symbols.keys())
            invalid_symbols = set(symbols_list) - valid_symbol_set

        finally:
            self.ibkr_client.disconnect()

        # Final statistics
        elapsed_time = time.time() - start_time
        success_rate = successful_validations / len(symbols_list) * 100
        validation_rate = len(symbols_list) / elapsed_time if elapsed_time > 0 else 0

        logger.info(f"🎯 Concurrent validation completed!")
        logger.info(f"✅ Valid: {successful_validations}")
        logger.info(f"❌ Invalid: {failed_validations}")
        logger.info(f"📈 Success rate: {success_rate:.1f}%")
        logger.info(f"⚡ Validation rate: {validation_rate:.1f} symbols/second")
        logger.info(f"⏱️  Total time: {elapsed_time:.1f} seconds")

        # Auto-cleanup invalid symbols from symbols_list.txt
        if invalid_symbols:
            self._cleanup_invalid_symbols_from_file(invalid_symbols)

        return {"valid": valid_symbols, "invalid": invalid_symbols}

    def _load_existing_stock_info(self) -> Dict[str, Dict]:
        """Load existing stock_info.csv for incremental updates"""
        stock_info_file = "data_store/stock_info.csv"
        existing_data = {}

        if os.path.exists(stock_info_file):
            try:
                import pandas as pd

                df = pd.read_csv(stock_info_file)

                for _, row in df.iterrows():
                    symbol = row["symbol"]

                    # Keep original IBKR classifications (no normalization)
                    existing_data[symbol] = {
                        "sector": row.get("sector", "Unknown"),
                        "industry": row.get("industry", "Unknown"),
                        "subcategory": row.get("subcategory", "Unknown"),
                        "long_name": row.get("long_name", symbol),
                        "currency": row.get("currency", "USD"),
                        "data_source": row.get("data_source", "Unknown"),
                        "last_updated": row.get("last_updated", ""),
                    }

                logger.info(f"Loaded existing data for {len(existing_data)} stocks")
                return existing_data

            except Exception as e:
                logger.warning(f"Failed to load existing stock_info.csv: {e}")

        return existing_data

    def _needs_classification_update(
        self, symbol: str, existing_data: Dict, days_threshold: int = 30
    ) -> bool:
        """Check if a symbol needs classification update"""
        if symbol not in existing_data:
            return True

        stock_info = existing_data[symbol]

        # Always update if classification is unknown or incomplete
        if (
            stock_info.get("sector") == "Unknown"
            or stock_info.get("industry") == "Unknown"
            or stock_info.get("data_source") != "IBKR_API"
        ):
            return True

        # Check if data is too old
        last_updated = stock_info.get("last_updated", "")
        if last_updated:
            try:
                from datetime import datetime, timedelta

                last_update_date = datetime.fromisoformat(
                    last_updated.replace("Z", "+00:00")
                )
                age_days = (datetime.now() - last_update_date).days
                return age_days >= days_threshold
            except:
                return True  # If we can't parse date, update it

        return True  # No last_updated info, needs update

    async def validate_and_classify_with_incremental_update(
        self, symbols: Set[str], max_concurrent: int = 30
    ) -> Dict:
        """Validate symbols and classify with incremental updates to minimize API calls"""
        logger.info(
            f"🎯 Starting incremental validation and classification of {len(symbols)} symbols"
        )

        # Load existing stock info
        existing_data = self._load_existing_stock_info()

        # Determine which symbols need updates
        symbols_to_update = set()
        symbols_to_keep = {}

        for symbol in symbols:
            if self._needs_classification_update(
                symbol, existing_data, days_threshold=30
            ):
                symbols_to_update.add(symbol)
            else:
                # Keep existing data
                symbols_to_keep[symbol] = existing_data[symbol]

        logger.info(f"📊 Incremental update analysis:")
        logger.info(f"   🔄 Need updates: {len(symbols_to_update)} symbols")
        logger.info(f"   ✅ Keep existing: {len(symbols_to_keep)} symbols")
        logger.info(
            f"   📈 API savings: {len(symbols_to_keep)}/{len(symbols)} ({len(symbols_to_keep)/len(symbols)*100:.1f}%)"
        )

        # Only validate/classify symbols that need updates
        if symbols_to_update:
            logger.info(
                f"🚀 Processing {len(symbols_to_update)} symbols that need updates..."
            )
            validation_results = await self.validate_symbols_with_ibkr_concurrent(
                symbols_to_update, max_concurrent
            )

            # Combine results
            all_valid_symbols = {**symbols_to_keep, **validation_results["valid"]}
            invalid_symbols = validation_results["invalid"]
        else:
            logger.info("✅ All symbols have current data, no API calls needed!")
            all_valid_symbols = symbols_to_keep
            invalid_symbols = set()

        return {"valid": all_valid_symbols, "invalid": invalid_symbols}

    def _cleanup_invalid_symbols_from_file(self, invalid_symbols: Set[str]):
        """Remove invalid symbols from symbols_list.txt"""
        symbols_file = "data_store/symbols_list.txt"

        if not os.path.exists(symbols_file):
            logger.warning(f"Symbols file {symbols_file} not found, skipping cleanup")
            return

        try:
            # Read current symbols
            with open(symbols_file, "r") as f:
                current_symbols = set(line.strip() for line in f if line.strip())

            # Remove invalid symbols
            cleaned_symbols = current_symbols - invalid_symbols
            removed_count = len(current_symbols) - len(cleaned_symbols)

            if removed_count > 0:
                # Backup original file
                backup_file = f"backups/{symbols_file}.backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
                os.makedirs("backups", exist_ok=True)

                import shutil

                shutil.copy2(symbols_file, backup_file)
                logger.info(f"Backed up {symbols_file} to {backup_file}")

                # Write cleaned symbols back
                with open(symbols_file, "w") as f:
                    for symbol in sorted(cleaned_symbols):
                        f.write(f"{symbol}\n")

                logger.info(
                    f"🧹 Cleaned up {removed_count} invalid symbols from {symbols_file}"
                )
                logger.info(f"📊 Remaining symbols: {len(cleaned_symbols)}")

                # Log the removed symbols for reference
                if len(invalid_symbols) <= 20:  # Don't spam log for too many symbols
                    logger.info(
                        f"❌ Removed symbols: {', '.join(sorted(invalid_symbols))}"
                    )
                else:
                    logger.info(
                        f"❌ Removed {len(invalid_symbols)} invalid symbols (too many to list)"
                    )
            else:
                logger.info("✅ No invalid symbols to remove from symbols_list.txt")

        except Exception as e:
            logger.error(f"Failed to cleanup invalid symbols: {e}")

    async def validate_symbols_with_ibkr(
        self, symbols: Set[str], _batch_size: int = 100
    ) -> Dict:
        """Validate symbols with IBKR - automatically choose concurrent or sequential based on size"""

        if len(symbols) > 50:  # Use concurrent for large batches
            logger.info(f"🚀 Using concurrent validation for {len(symbols)} symbols")
            return await self.validate_symbols_with_ibkr_concurrent(
                symbols, max_concurrent=30
            )
        else:
            logger.info(f"📝 Using sequential validation for {len(symbols)} symbols")
            return await self._validate_symbols_sequential(symbols, 100)

    async def _validate_symbols_sequential(
        self, symbols: Set[str], batch_size: int = 100
    ) -> Dict:
        """Sequential validation for small batches (fallback method)"""
        logger.info(f"Validating {len(symbols)} symbols with IBKR (sequential)...")

        if not await self.ibkr_client.connect():
            logger.error("Failed to connect to IBKR")
            return {"valid": {}, "invalid": set()}

        symbols_list = list(symbols)
        sector_mapping = self.get_sector_industry_mapping()

        valid_symbols = {}
        invalid_symbols = set()

        try:
            for symbol in symbols_list:
                try:
                    # Try to get historical data to validate symbol
                    df = await self.ibkr_client.get_historical_data(
                        symbol, "5 D", "1 day"
                    )

                    if df is not None and not df.empty:
                        # Symbol is valid
                        if symbol in sector_mapping:
                            classification = sector_mapping[symbol]
                        else:
                            classification = self.get_default_classification(symbol)

                        valid_symbols[symbol] = {
                            "sector": classification["sector"],
                            "industry": classification["industry"],
                            "currency": "USD",
                        }
                    else:
                        invalid_symbols.add(symbol)

                    # Rate limiting
                    await asyncio.sleep(0.1)

                except Exception as e:
                    invalid_symbols.add(symbol)
                    logger.debug(f"Symbol {symbol} validation failed: {e}")

        finally:
            self.ibkr_client.disconnect()

        logger.info(
            f"Validation complete: {len(valid_symbols)} valid, {len(invalid_symbols)} invalid"
        )

        # Auto-cleanup invalid symbols from symbols_list.txt
        if invalid_symbols:
            self._cleanup_invalid_symbols_from_file(invalid_symbols)

        return {"valid": valid_symbols, "invalid": invalid_symbols}

    def update_files(self, valid_symbols: Dict):
        """Update both stock_info.csv and symbols_list.txt with comprehensive classification data"""
        # Update stock_info.csv with enhanced classification data
        csv_data = []
        for symbol, info in valid_symbols.items():
            csv_data.append(
                {
                    "symbol": symbol,
                    "currency": info.get("currency", "USD"),
                    "sector": info.get("sector", "Unknown"),
                    "industry": info.get("industry", "Unknown"),
                    "subcategory": info.get("subcategory", "Unknown"),
                    "long_name": info.get("long_name", symbol),
                    "data_source": info.get("data_source", "IBKR_API"),
                    "last_updated": info.get(
                        "last_updated", datetime.now().isoformat()
                    ),
                }
            )

        csv_data.sort(key=lambda x: x["symbol"])

        # Enhanced CSV with more classification fields
        fieldnames = [
            "symbol",
            "currency",
            "sector",
            "industry",
            "subcategory",
            "long_name",
            "data_source",
            "last_updated",
        ]

        try:
            # Create backup of existing file
            if os.path.exists(self.stock_info_file):
                backup_file = f"{self.stock_info_file}.backup"
                import shutil
                shutil.copy2(self.stock_info_file, backup_file)
                logger.info(f"Created backup: {backup_file}")

            # Write new stock info file
            with open(self.stock_info_file, "w", newline="", encoding="utf-8") as f:
                writer = csv.DictWriter(f, fieldnames=fieldnames)
                writer.writeheader()
                writer.writerows(csv_data)

            logger.info(
                f"Updated {self.stock_info_file} with {len(csv_data)} symbols and precise classifications"
            )

        except Exception as e:
            logger.error(f"Failed to write stock_info.csv: {e}")
            # Restore from backup if available
            backup_file = f"{self.stock_info_file}.backup"
            if os.path.exists(backup_file):
                import shutil
                shutil.copy2(backup_file, self.stock_info_file)
                logger.info("Restored from backup")
            raise

        try:
            # Create backup of symbols file
            if os.path.exists(self.symbols_file):
                backup_file = f"{self.symbols_file}.backup"
                import shutil
                shutil.copy2(self.symbols_file, backup_file)

            # Update symbols_list.txt
            sorted_symbols = sorted(valid_symbols.keys())

            with open(self.symbols_file, "w") as f:
                f.write(" ".join(sorted_symbols) + "\n")

            logger.info(f"Updated {self.symbols_file} with {len(sorted_symbols)} symbols")

        except Exception as e:
            logger.error(f"Failed to write symbols_list.txt: {e}")
            # Restore from backup if available
            backup_file = f"{self.symbols_file}.backup"
            if os.path.exists(backup_file):
                import shutil
                shutil.copy2(backup_file, self.symbols_file)
                logger.info("Restored symbols file from backup")
            raise

    def generate_update_report(self, results: Dict):
        """Generate comprehensive update report"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        report_file = f"stock_universe_update_report_{timestamp}.txt"

        with open(report_file, "w") as f:
            f.write("STOCK UNIVERSE UPDATE REPORT\n")
            f.write("=" * 60 + "\n\n")
            f.write(f"Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")

            f.write("SUMMARY:\n")
            f.write(f"Valid symbols: {len(results['valid'])}\n")
            f.write(f"Invalid symbols: {len(results['invalid'])}\n")
            f.write(
                f"Success rate: {len(results['valid']) / (len(results['valid']) + len(results['invalid'])) * 100:.1f}%\n\n"
            )

            # Sector breakdown
            sector_counts = {}
            for symbol, info in results["valid"].items():
                sector = info["sector"]
                sector_counts[sector] = sector_counts.get(sector, 0) + 1

            f.write("SECTOR BREAKDOWN:\n")
            for sector, count in sorted(
                sector_counts.items(), key=lambda x: x[1], reverse=True
            ):
                f.write(f"{sector}: {count} symbols\n")
            f.write("\n")

            f.write("FILES UPDATED:\n")
            f.write(
                f"- {self.stock_info_file}: {len(results['valid'])} symbols with sector/industry data\n"
            )
            f.write(
                f"- {self.symbols_file}: {len(results['valid'])} symbols for trading system\n\n"
            )

            if results["invalid"]:
                f.write(f"INVALID SYMBOLS (first 50):\n")
                for symbol in sorted(list(results["invalid"])[:50]):
                    f.write(f"{symbol}\n")

        logger.info(f"Generated update report: {report_file}")

    async def run_full_update(self, max_symbols: Optional[int] = None):
        """Run complete stock universe update using Yahoo Finance and comprehensive sources"""
        logger.info("Starting comprehensive stock universe update...")
        logger.info("🚀 YAHOO FINANCE MODE: Using Yahoo Finance + comprehensive sources for NYSE/NASDAQ stocks")
        logger.info("📈 Primary source: Yahoo Finance and external APIs")
        logger.info("🎯 Getting complete NYSE and NASDAQ stock universe")
        logger.info("=" * 60)

        # Backup existing files
        self.backup_existing_files()

        # Get comprehensive symbol list (NEW: Comprehensive IBKR scanner + fallback sources)
        logger.info("🔍 Phase 1: Comprehensive stock symbol collection...")
        all_symbols = await self.get_enhanced_symbol_sources()

        # Limit symbols if specified (for testing)
        if max_symbols and len(all_symbols) > max_symbols:
            logger.info(f"Limiting to {max_symbols} symbols for this update")
            all_symbols = set(list(all_symbols)[:max_symbols])

        # Validate symbols with IBKR and get precise classifications (with incremental updates)
        logger.info("🔍 Phase 2: Symbol validation and classification...")
        results = await self.validate_and_classify_with_incremental_update(
            all_symbols, max_concurrent=30
        )

        # Update files
        logger.info("📝 Phase 3: Updating stock universe files...")
        self.update_files(results["valid"])

        # Generate report
        logger.info("📊 Phase 4: Generating update report...")
        self.generate_update_report(results)

        logger.info("=" * 60)
        logger.info("✅ Stock universe update completed!")
        logger.info(f"📊 Total valid symbols: {len(results['valid'])}")
        logger.info(f"📁 Files updated: {self.stock_info_file}, {self.symbols_file}")
        logger.info("🎯 Data sourced from Yahoo Finance + comprehensive external sources")
        logger.info("📈 Complete NYSE and NASDAQ stock coverage achieved")


async def main():
    """Main function"""
    import argparse

    parser = argparse.ArgumentParser(description="Stock Universe Updater")
    parser.add_argument(
        "--max-symbols", type=int, help="Maximum symbols to process (for testing)"
    )
    parser.add_argument(
        "--full-update", action="store_true", help="Run full universe update"
    )

    args = parser.parse_args()

    updater = StockUniverseUpdater()

    if args.full_update:
        await updater.run_full_update(args.max_symbols)
    else:
        print("Use --full-update to refresh the complete stock universe")
        print(
            "Example: python update_stock_universe.py --full-update --max-symbols 1000"
        )


if __name__ == "__main__":
    asyncio.run(main())
