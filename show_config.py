#!/usr/bin/env python3
"""
配置展示脚本

展示所有可用的配置选项和使用方法
"""

import sys
import os

# 添加当前目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def show_config_overview():
    """展示配置概览"""
    print("🚀 智能股票交易系统 - 配置概览")
    print("=" * 80)
    print()
    
    print("📋 主要配置类：")
    print("   🔌 IBKRConfig              - IBKR连接和交易配置")
    print("   ⚡ IBKRPerformanceConfig   - IBKR性能优化配置")
    print("   🧠 ModelConfig             - 机器学习模型配置")
    print("   📊 TechnicalIndicatorConfig - 技术指标配置")
    print("   📈 TrendClassificationConfig - 趋势分类配置")
    print("   🤖 BotConfig系列           - 交易机器人配置")
    print("   🛡️ RiskConfig              - 风险管理配置")
    print("   📈 OptionsConfig           - 期权交易配置")
    print("   📊 MonitoringConfig        - 系统监控配置")
    print()

def show_model_options():
    """展示模型配置选项"""
    print("🧠 模型配置选项")
    print("=" * 50)
    print()
    
    print("🔬 模型类型 (model_type):")
    print("   • 'lstm'     - LSTM模型（稳定，适合新手）")
    print("   • 'cnn_gru'  - CNN-GRU混合模型（推荐，性能更好）")
    print("   • 'ensemble' - 集成模型（LSTM + CNN-GRU平均预测）")
    print()
    
    print("⚖️ 集成预测 (enable_ensemble):")
    print("   • True  - 启用集成预测，训练两个模型取平均")
    print("   • False - 使用单一模型")
    print()
    
    print("🎛️ 集成权重 (ensemble_weights):")
    print("   • [0.4, 0.6] - LSTM权重40%，CNN-GRU权重60%（推荐）")
    print("   • [0.5, 0.5] - 等权重")
    print("   • [0.3, 0.7] - 更偏向CNN-GRU")
    print()
    
    print("💡 使用示例：")
    print("   # 使用CNN-GRU模型")
    print("   model_config = ModelConfig(model_type='cnn_gru')")
    print()
    print("   # 使用集成模型")
    print("   model_config = ModelConfig(")
    print("       model_type='ensemble',")
    print("       enable_ensemble=True,")
    print("       ensemble_weights=[0.4, 0.6]")
    print("   )")
    print()

def show_trend_classification():
    """展示趋势分类配置"""
    print("📈 趋势分类配置")
    print("=" * 50)
    print()
    
    print("🎯 极端级别 (extreme_level):")
    print("   • 'high'   - 高标准（极端事件更稀少，约0.1-0.6%）")
    print("   • 'medium' - 中等标准（平衡机会和质量）")
    print("   • 'low'    - 低标准（更多交易机会）")
    print()
    
    print("📊 7级趋势分类：")
    print("   🔴 EXTREME BELOW TREND  - 极度超卖（0.1-0.6%）")
    print("   🟠 HIGHLY BELOW TREND   - 高度超卖（1-2%）")
    print("   🟡 BELOW TREND          - 轻度超卖（5-8%）")
    print("   🟢 ALONG TREND          - 正常趋势（38-79%）")
    print("   🟡 ABOVE TREND          - 轻度超买（5-8%）")
    print("   🟠 HIGHLY ABOVE TREND   - 高度超买（1-2%）")
    print("   🔴 EXTREME ABOVE TREND  - 极度超买（0.1-0.6%）")
    print()
    
    print("💡 使用示例：")
    print("   # 高标准设置（推荐）")
    print("   trend_config = TrendClassificationConfig(extreme_level='high')")
    print()

def show_bot_configurations():
    """展示机器人配置"""
    print("🤖 交易机器人配置")
    print("=" * 50)
    print()
    
    bots = {
        'Adam': {
            'type': '保守型',
            'strategy': '买入HIGHLY BELOW，卖出HIGHLY ABOVE',
            'risk': '低风险',
            'suitable': '稳定收益，新手推荐'
        },
        'Betty': {
            'type': '平衡型 ⭐',
            'strategy': '动态止盈止损（止盈10%，止损3%）',
            'risk': '中等风险',
            'suitable': '大多数用户，推荐默认选择'
        },
        'Chris': {
            'type': '大盘股专注',
            'strategy': '专注FAANG股票（GOOGL, AMZN, AAPL, MSFT, META）',
            'risk': '低风险',
            'suitable': '保守投资者，偏好大盘股'
        },
        'Dany': {
            'type': '激进型',
            'strategy': '快速交易，短期持有（最多5天）',
            'risk': '高风险',
            'suitable': '经验丰富的交易者'
        },
        'Eddy': {
            'type': '反向投资',
            'strategy': '逆向思维，买入EXTREME ABOVE',
            'risk': '高风险',
            'suitable': '专业投资者，市场老手'
        },
        'Flora': {
            'type': '长期价值',
            'strategy': '价值投资，长期持有（最少30天）',
            'risk': '低风险',
            'suitable': '长期投资者，价值投资理念'
        }
    }
    
    for bot_name, info in bots.items():
        print(f"🤖 {bot_name} - {info['type']}")
        print(f"   策略: {info['strategy']}")
        print(f"   风险: {info['risk']}")
        print(f"   适合: {info['suitable']}")
        print()
    
    print("💡 使用示例：")
    print("   # 获取Betty机器人配置")
    print("   bot_config = get_bot_config('Betty')")
    print()
    print("   # 在交易系统中使用")
    print("   python daily_trading_system.py --bot Betty --capital 100000")
    print()

def show_preset_configurations():
    """展示预设配置组合"""
    print("⚙️ 预设配置组合")
    print("=" * 50)
    print()
    
    print("🛡️ 保守配置 (Conservative):")
    print("   • 纸上交易模式")
    print("   • 最大仓位: $5,000")
    print("   • 止损: 3%, 止盈: 8%")
    print("   • 单股仓位: 3%")
    print("   • 模型: LSTM（稳定）")
    print("   • 机器人: Adam")
    print("   • 适合: 新手，学习阶段")
    print()
    
    print("⚖️ 平衡配置 (Balanced) ⭐:")
    print("   • 可选纸上/实盘交易")
    print("   • 最大仓位: $10,000")
    print("   • 止损: 5%, 止盈: 10%")
    print("   • 单股仓位: 5%")
    print("   • 模型: CNN-GRU（推荐）")
    print("   • 机器人: Betty")
    print("   • 适合: 大多数用户")
    print()
    
    print("🚀 激进配置 (Aggressive):")
    print("   • 实盘交易模式")
    print("   • 最大仓位: $20,000")
    print("   • 止损: 8%, 止盈: 15%")
    print("   • 单股仓位: 8%")
    print("   • 模型: 集成模型")
    print("   • 机器人: Dany")
    print("   • 适合: 经验丰富的交易者")
    print()
    
    print("💡 使用示例：")
    print("   # 获取平衡配置")
    print("   config = get_balanced_config()")
    print()
    print("   # 验证配置")
    print("   if validate_config(config):")
    print("       print_config_summary(config)")
    print()

def show_usage_examples():
    """展示使用示例"""
    print("💡 配置使用示例")
    print("=" * 50)
    print()
    
    print("🔧 基础配置创建：")
    print("```python")
    print("from config import *")
    print()
    print("# 创建IBKR配置")
    print("ibkr_config = IBKRConfig(")
    print("    host='127.0.0.1',")
    print("    port=7497,  # 纸上交易")
    print("    paper_trading=True,")
    print("    max_position_size=10000.0")
    print(")")
    print()
    print("# 创建模型配置")
    print("model_config = ModelConfig(")
    print("    model_type='cnn_gru',")
    print("    enable_ensemble=True,")
    print("    epochs=200")
    print(")")
    print("```")
    print()
    
    print("🎯 使用预设配置：")
    print("```python")
    print("# 获取平衡配置（推荐）")
    print("config = get_balanced_config()")
    print()
    print("# 获取保守配置（新手）")
    print("config = get_conservative_config()")
    print()
    print("# 获取激进配置（高级用户）")
    print("config = get_aggressive_config()")
    print("```")
    print()
    
    print("🤖 机器人配置：")
    print("```python")
    print("# 获取Betty机器人配置")
    print("betty_config = get_bot_config('Betty')")
    print()
    print("# 获取所有可用机器人")
    print("available_bots = list(BOT_CONFIGS.keys())")
    print("print(available_bots)  # ['Adam', 'Betty', 'Chris', 'Dany', 'Eddy', 'Flora']")
    print("```")
    print()
    
    print("✅ 配置验证：")
    print("```python")
    print("# 验证配置有效性")
    print("config = get_balanced_config()")
    print("if validate_config(config):")
    print("    print('✅ 配置有效')")
    print("    print_config_summary(config)")
    print("else:")
    print("    print('❌ 配置无效')")
    print("```")
    print()

def show_command_line_usage():
    """展示命令行使用方法"""
    print("🖥️ 命令行使用方法")
    print("=" * 50)
    print()
    
    print("🚀 启动交易系统：")
    print("```bash")
    print("# 纸上交易（推荐新手）")
    print("python daily_trading_system.py --paper --bot Betty --capital 100000")
    print()
    print("# 实盘交易（谨慎使用）")
    print("python daily_trading_system.py --live --bot Betty --capital 50000")
    print()
    print("# 指定极端级别")
    print("python daily_trading_system.py --paper --extreme-level high")
    print()
    print("# 启用期权交易")
    print("python daily_trading_system.py --paper --enable-options")
    print("```")
    print()
    
    print("📊 数据和模型：")
    print("```bash")
    print("# 更新股票池")
    print("python update_stock_universe.py")
    print()
    print("# 下载历史数据")
    print("python ibkr_data_provider.py")
    print()
    print("# 训练回归预测模型")
    print("python enhanced_reversion_prediction.py")
    print()
    print("# 模型性能比较")
    print("python model_comparison_test.py")
    print("```")
    print()

def main():
    """主函数"""
    print()
    show_config_overview()
    show_model_options()
    show_trend_classification()
    show_bot_configurations()
    show_preset_configurations()
    show_usage_examples()
    show_command_line_usage()
    
    print("🎉 配置说明完成！")
    print()
    print("📚 更多信息请参考：")
    print("   • README.md - 完整系统文档")
    print("   • QUICK_REFERENCE.md - 快速参考指南")
    print("   • config.py - 配置文件源码")
    print()
    print("🔧 测试配置：")
    print("   python test_config.py")

if __name__ == "__main__":
    main()
