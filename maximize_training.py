#!/usr/bin/env python3
"""
最大化训练配置脚本 - 下载全部股票5年数据，不限制训练对数
"""

import subprocess
import sys
import os
from datetime import datetime

def check_data_status():
    """检查当前数据状态"""
    print("🔍 Checking current data status...")
    
    cache_files = [
        "cache/stock_data/incremental_data.pkl",
        "cache/stock_data/stock_data_5y.pkl"
    ]
    
    for cache_file in cache_files:
        if os.path.exists(cache_file):
            size_mb = os.path.getsize(cache_file) / (1024 * 1024)
            print(f"   ✅ Found: {cache_file} ({size_mb:.1f} MB)")
        else:
            print(f"   ❌ Missing: {cache_file}")
    
    return any(os.path.exists(f) for f in cache_files)

def download_full_5year_data():
    """下载全部股票5年数据"""
    print("\n🚀 Downloading FULL 5-year data for ALL stocks...")
    print("⚠️  This will take 2-4 hours and download ~200-500MB of data")
    
    confirm = input("Continue with full data download? (y/N): ").strip().lower()
    if confirm != 'y':
        return False
    
    # 使用最大化参数下载数据
    cmd = [
        'python', 'enhanced_reversion_prediction.py',
        '--duration-years', '5',
        '--max-stocks', '10000',  # 下载所有可用股票
        '--max-pairs', '10',      # 只分析少量对以快速完成
        '--epochs', '1',          # 只训练1轮以快速完成
        '--no-save',              # 不保存模型，只下载数据
        '--strategy', 'aggressive',
        '--extreme-level', 'high'
    ]
    
    print(f"📋 Command: {' '.join(cmd)}")
    
    try:
        result = subprocess.run(cmd, timeout=14400)  # 4小时超时
        
        if result.returncode == 0:
            print("✅ Full data download completed successfully!")
            return True
        else:
            print("❌ Data download failed!")
            return False
            
    except subprocess.TimeoutExpired:
        print("⏰ Data download timed out (4 hours)")
        return False
    except KeyboardInterrupt:
        print("🛑 Data download interrupted by user")
        return False
    except Exception as e:
        print(f"💥 Data download crashed: {e}")
        return False

def run_maximized_training():
    """运行最大化训练"""
    print("\n🎯 Running MAXIMIZED training with full data...")
    
    # 最大化训练配置
    configs = [
        {
            "name": "Maximum Aggressive Training",
            "description": "使用所有数据的激进策略训练",
            "command": [
                'python', 'enhanced_reversion_prediction.py',
                '--duration-years', '5',
                '--max-stocks', '5000',           # 使用5000只股票
                '--use-all-correlations',         # 使用所有高相关性股票对
                '--min-correlation-level', 'high', # 最低高相关性
                '--strategy', 'aggressive',
                '--extreme-level', 'high',
                '--model-type', 'ensemble',       # 使用最强模型
                '--epochs', '300',                # 充分训练
                '--save-model', 'max_aggressive_5y'
            ]
        },
        {
            "name": "Maximum Balanced Training", 
            "description": "使用所有数据的平衡策略训练",
            "command": [
                'python', 'enhanced_reversion_prediction.py',
                '--duration-years', '5',
                '--max-stocks', '5000',
                '--use-all-correlations',
                '--min-correlation-level', 'high',
                '--strategy', 'balanced',
                '--extreme-level', 'high',
                '--model-type', 'cnn_gru',
                '--epochs', '250',
                '--save-model', 'max_balanced_5y'
            ]
        },
        {
            "name": "Extreme High Correlation Focus",
            "description": "专注极高相关性股票对的深度训练",
            "command": [
                'python', 'enhanced_reversion_prediction.py',
                '--duration-years', '5',
                '--max-stocks', '3000',
                '--use-all-correlations',
                '--min-correlation-level', 'very_high',  # 只用极高相关性
                '--strategy', 'aggressive',
                '--extreme-level', 'high',
                '--model-type', 'ensemble',
                '--epochs', '400',                # 更深度训练
                '--save-model', 'max_extreme_corr_5y'
            ]
        }
    ]
    
    print(f"\n📊 Available maximized training configurations:")
    for i, config in enumerate(configs, 1):
        print(f"{i}. {config['name']}")
        print(f"   {config['description']}")
    
    print("4. Run all configurations")
    print("0. Exit")
    
    choice = input("\nSelect configuration (0-4): ").strip()
    
    if choice == '0':
        return
    elif choice == '4':
        # 运行所有配置
        for config in configs:
            print(f"\n🚀 Running: {config['name']}")
            print(f"📝 {config['description']}")
            print(f"💻 Command: {' '.join(config['command'])}")
            
            confirm = input("Run this configuration? (y/N): ").strip().lower()
            if confirm == 'y':
                try:
                    result = subprocess.run(config['command'], timeout=7200)  # 2小时超时
                    if result.returncode == 0:
                        print(f"✅ {config['name']} completed successfully!")
                    else:
                        print(f"❌ {config['name']} failed!")
                except Exception as e:
                    print(f"💥 {config['name']} crashed: {e}")
    else:
        try:
            config_idx = int(choice) - 1
            if 0 <= config_idx < len(configs):
                config = configs[config_idx]
                print(f"\n🚀 Running: {config['name']}")
                print(f"📝 {config['description']}")
                print(f"💻 Command: {' '.join(config['command'])}")
                
                confirm = input("Run this configuration? (y/N): ").strip().lower()
                if confirm == 'y':
                    result = subprocess.run(config['command'], timeout=7200)
                    if result.returncode == 0:
                        print(f"✅ {config['name']} completed successfully!")
                    else:
                        print(f"❌ {config['name']} failed!")
            else:
                print("❌ Invalid selection")
        except ValueError:
            print("❌ Invalid input")

def show_maximized_config_guide():
    """显示最大化配置指南"""
    print("\n📚 Maximized Training Configuration Guide")
    print("=" * 80)
    
    print("\n🎯 最大化训练的关键参数:")
    print("   --duration-years 5           # 使用5年历史数据")
    print("   --max-stocks 5000            # 使用5000只股票（vs 默认1000）")
    print("   --use-all-correlations       # 使用所有高相关性股票对")
    print("   --min-correlation-level high # 最低高相关性级别")
    print("   --strategy aggressive        # 激进策略")
    print("   --extreme-level high         # 高极端级别")
    print("   --model-type ensemble        # 最强模型")
    print("   --epochs 300                 # 充分训练")
    
    print("\n📊 预期数据规模:")
    print("   • 股票数量: 5000只 (vs 默认1000只)")
    print("   • 数据年数: 5年 (vs 默认3年)")
    print("   • K线数量: 1260根/股票 (vs 756根)")
    print("   • 相关性对: ~2000对 (vs 默认200对)")
    print("   • 总数据量: ~500MB (vs ~100MB)")
    
    print("\n⏱️ 时间估算:")
    print("   • 数据下载: 2-4小时 (一次性)")
    print("   • 单次训练: 1-3小时")
    print("   • 全部训练: 6-12小时")
    
    print("\n🚀 快速开始命令:")
    print("\n# 1. 下载全部5年数据")
    print("python maximize_training.py")
    print("# 选择选项1: Download full 5-year data")
    
    print("\n# 2. 运行最大化训练")
    print("python enhanced_reversion_prediction.py \\")
    print("    --duration-years 5 \\")
    print("    --max-stocks 5000 \\")
    print("    --use-all-correlations \\")
    print("    --min-correlation-level high \\")
    print("    --strategy aggressive \\")
    print("    --extreme-level high \\")
    print("    --model-type ensemble \\")
    print("    --epochs 300")
    
    print("\n💡 优化建议:")
    print("   • 首次运行: 使用较少epochs (50-100) 验证配置")
    print("   • 生产训练: 使用300+ epochs 获得最佳效果")
    print("   • 内存不足: 减少max-stocks到3000或2000")
    print("   • 时间有限: 使用min-correlation-level very_high")

def main():
    """主函数"""
    print("🎯 Maximize Training Configuration Tool")
    print("=" * 80)
    print("Purpose: Download ALL stocks 5-year data and maximize training")
    
    print("\n选择操作:")
    print("1. Download full 5-year data for ALL stocks")
    print("2. Run maximized training (requires data)")
    print("3. Show configuration guide")
    print("4. Check current data status")
    print("0. Exit")
    
    while True:
        choice = input("\n请选择 (0-4): ").strip()
        
        if choice == '0':
            print("👋 再见!")
            break
        elif choice == '1':
            download_full_5year_data()
        elif choice == '2':
            if check_data_status():
                run_maximized_training()
            else:
                print("❌ No data found. Please download data first (option 1)")
        elif choice == '3':
            show_maximized_config_guide()
        elif choice == '4':
            check_data_status()
        else:
            print("❌ 无效选择，请重试")

if __name__ == "__main__":
    main()
