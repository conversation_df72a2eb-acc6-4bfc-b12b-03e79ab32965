#!/usr/bin/env python3
"""
缓存配置和数据质量管理

管理缓存目录结构和数据质量要求
"""

import os
from typing import Dict, Tuple

class CacheConfig:
    """缓存配置管理"""
    
    def __init__(self, base_dir: str = "cache"):
        self.base_dir = base_dir
        
        # 缓存子目录结构
        self.cache_dirs = {
            "stock_data": os.path.join(base_dir, "stock_data"),      # 股票数据缓存
            "model_cache": os.path.join(base_dir, "model_cache"),    # 模型训练缓存
            "analysis": os.path.join(base_dir, "analysis"),          # 分析结果缓存
            "temp": os.path.join(base_dir, "temp"),                  # 临时文件
            "progress": os.path.join(base_dir, "progress"),          # 进度文件
        }
        
        # 数据质量要求 (年数 -> 最小bars数量)
        self.min_bars_requirements = {
            1: 240,   # 1年数据至少240根bars (约1个交易年)
            2: 480,   # 2年数据至少480根bars (约2个交易年)
            3: 720,   # 3年数据至少720根bars (约3个交易年)
            4: 960,   # 4年数据至少960根bars (约4个交易年)
            5: 1200,  # 5年数据至少1200根bars (约5个交易年)
        }
        
        # 数据质量阈值
        self.quality_thresholds = {
            "min_price": 0.01,           # 最小价格
            "max_missing_ratio": 0.05,   # 最大缺失值比例 (5%)
            "min_volume": 1,             # 最小成交量
            "max_zero_volume_ratio": 0.1, # 最大零成交量比例 (10%)
        }
        
        # 创建所有缓存目录
        self._create_cache_directories()
    
    def _create_cache_directories(self):
        """创建所有缓存目录"""
        for dir_name, dir_path in self.cache_dirs.items():
            os.makedirs(dir_path, exist_ok=True)
            print(f"📁 Created cache directory: {dir_path}")
    
    def get_cache_path(self, cache_type: str, filename: str = None) -> str:
        """获取缓存文件路径"""
        if cache_type not in self.cache_dirs:
            raise ValueError(f"Unknown cache type: {cache_type}")
        
        base_path = self.cache_dirs[cache_type]
        
        if filename:
            return os.path.join(base_path, filename)
        else:
            return base_path
    
    def get_min_bars_requirement(self, duration_years: int) -> int:
        """获取指定年数的最小bars要求"""
        if duration_years in self.min_bars_requirements:
            return self.min_bars_requirements[duration_years]
        else:
            # 对于未定义的年数，按比例计算
            return duration_years * 240
    
    def validate_stock_data_quality(self, prices: list, volumes: list, duration_years: int) -> Tuple[bool, str]:
        """
        验证股票数据质量
        
        Returns:
            (is_valid, reason)
        """
        if not prices or not volumes:
            return False, "Empty price or volume data"
        
        # 检查数据长度
        min_bars = self.get_min_bars_requirement(duration_years)
        if len(prices) < min_bars:
            return False, f"Insufficient data: {len(prices)} < {min_bars} bars required for {duration_years} years"
        
        # 检查价格有效性
        valid_prices = [p for p in prices if p is not None and p > self.quality_thresholds["min_price"]]
        if len(valid_prices) < len(prices) * (1 - self.quality_thresholds["max_missing_ratio"]):
            return False, f"Too many invalid prices: {len(prices) - len(valid_prices)}/{len(prices)}"
        
        # 检查成交量有效性
        valid_volumes = [v for v in volumes if v is not None and v >= self.quality_thresholds["min_volume"]]
        zero_volume_count = sum(1 for v in volumes if v == 0)
        zero_volume_ratio = zero_volume_count / len(volumes)
        
        if zero_volume_ratio > self.quality_thresholds["max_zero_volume_ratio"]:
            return False, f"Too many zero volume days: {zero_volume_ratio:.2%} > {self.quality_thresholds['max_zero_volume_ratio']:.2%}"
        
        return True, "Data quality passed"
    
    def filter_stocks_by_quality(self, stock_data: dict, duration_years: int) -> Tuple[dict, dict]:
        """
        根据数据质量过滤股票
        
        Returns:
            (filtered_data, quality_report)
        """
        if not stock_data or 'tickers' not in stock_data:
            return stock_data, {"error": "Invalid stock data format"}
        
        filtered_data = {
            "tickers": [],
            "price": [],
            "volume": [],
            "currencies": [],
            "sectors": {},
            "industries": {},
            "dates": stock_data.get("dates", []),
            "exchange_rates": stock_data.get("exchange_rates", {}),
            "default_currency": stock_data.get("default_currency", "USD"),
        }
        
        quality_report = {
            "total_stocks": len(stock_data["tickers"]),
            "passed_stocks": 0,
            "failed_stocks": 0,
            "failure_reasons": {},
            "min_bars_required": self.get_min_bars_requirement(duration_years),
        }
        
        for i, ticker in enumerate(stock_data["tickers"]):
            try:
                prices = stock_data["price"][i] if i < len(stock_data["price"]) else []
                volumes = stock_data["volume"][i] if i < len(stock_data["volume"]) else []
                
                is_valid, reason = self.validate_stock_data_quality(prices, volumes, duration_years)
                
                if is_valid:
                    # 添加到过滤后的数据
                    filtered_data["tickers"].append(ticker)
                    filtered_data["price"].append(prices)
                    filtered_data["volume"].append(volumes)
                    filtered_data["currencies"].append(
                        stock_data["currencies"][i] if i < len(stock_data["currencies"]) else "USD"
                    )
                    filtered_data["sectors"][ticker] = stock_data.get("sectors", {}).get(ticker, "Unknown")
                    filtered_data["industries"][ticker] = stock_data.get("industries", {}).get(ticker, "Unknown")
                    
                    quality_report["passed_stocks"] += 1
                else:
                    quality_report["failed_stocks"] += 1
                    if reason not in quality_report["failure_reasons"]:
                        quality_report["failure_reasons"][reason] = 0
                    quality_report["failure_reasons"][reason] += 1
                    
            except Exception as e:
                quality_report["failed_stocks"] += 1
                error_reason = f"Processing error: {str(e)}"
                if error_reason not in quality_report["failure_reasons"]:
                    quality_report["failure_reasons"][error_reason] = 0
                quality_report["failure_reasons"][error_reason] += 1
        
        return filtered_data, quality_report
    
    def print_quality_report(self, quality_report: dict):
        """打印数据质量报告"""
        print("\n📊 Data Quality Report")
        print("=" * 50)
        print(f"📈 Total stocks analyzed: {quality_report['total_stocks']}")
        print(f"✅ Passed quality check: {quality_report['passed_stocks']}")
        print(f"❌ Failed quality check: {quality_report['failed_stocks']}")
        print(f"📏 Minimum bars required: {quality_report['min_bars_required']}")
        
        if quality_report['failed_stocks'] > 0:
            print(f"\n❌ Failure reasons:")
            for reason, count in quality_report['failure_reasons'].items():
                print(f"   • {reason}: {count} stocks")
        
        success_rate = quality_report['passed_stocks'] / quality_report['total_stocks'] * 100
        print(f"\n🎯 Success rate: {success_rate:.1f}%")
    
    def get_stock_data_cache_path(self, duration_years: int) -> str:
        """获取股票数据缓存文件路径"""
        filename = f"stock_data_{duration_years}y.pkl"
        return self.get_cache_path("stock_data", filename)
    
    def get_model_cache_path(self, model_type: str, duration_years: int) -> str:
        """获取模型缓存文件路径"""
        filename = f"model_{model_type}_{duration_years}y.pkl"
        return self.get_cache_path("model_cache", filename)
    
    def get_analysis_cache_path(self, analysis_type: str, duration_years: int) -> str:
        """获取分析结果缓存文件路径"""
        filename = f"analysis_{analysis_type}_{duration_years}y.pkl"
        return self.get_cache_path("analysis", filename)
    
    def get_progress_file_path(self, task_name: str) -> str:
        """获取进度文件路径"""
        filename = f"progress_{task_name}.json"
        return self.get_cache_path("progress", filename)
    
    def cleanup_old_cache(self, keep_days: int = 7):
        """清理旧的缓存文件"""
        import time
        import glob
        
        current_time = time.time()
        cutoff_time = current_time - (keep_days * 24 * 3600)
        
        cleaned_count = 0
        
        for cache_type, cache_dir in self.cache_dirs.items():
            if cache_type == "temp":  # 临时文件目录清理更频繁
                temp_cutoff = current_time - (1 * 24 * 3600)  # 1天
                for file_path in glob.glob(os.path.join(cache_dir, "*")):
                    if os.path.getmtime(file_path) < temp_cutoff:
                        try:
                            os.remove(file_path)
                            cleaned_count += 1
                        except Exception as e:
                            print(f"⚠️ Failed to remove {file_path}: {e}")
            else:
                for file_path in glob.glob(os.path.join(cache_dir, "*")):
                    if os.path.getmtime(file_path) < cutoff_time:
                        try:
                            os.remove(file_path)
                            cleaned_count += 1
                        except Exception as e:
                            print(f"⚠️ Failed to remove {file_path}: {e}")
        
        if cleaned_count > 0:
            print(f"🧹 Cleaned {cleaned_count} old cache files")


# 全局缓存配置实例
cache_config = CacheConfig()

# 便捷函数
def get_cache_path(cache_type: str, filename: str = None) -> str:
    """获取缓存路径的便捷函数"""
    return cache_config.get_cache_path(cache_type, filename)

def validate_stock_quality(prices: list, volumes: list, duration_years: int) -> Tuple[bool, str]:
    """验证股票数据质量的便捷函数"""
    return cache_config.validate_stock_data_quality(prices, volumes, duration_years)

def filter_stocks_by_quality(stock_data: dict, duration_years: int) -> Tuple[dict, dict]:
    """过滤股票数据的便捷函数"""
    return cache_config.filter_stocks_by_quality(stock_data, duration_years)

def get_min_bars_requirement(duration_years: int) -> int:
    """获取最小bars要求的便捷函数"""
    return cache_config.get_min_bars_requirement(duration_years)
