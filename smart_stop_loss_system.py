#!/usr/bin/env python3
"""
Smart Stop Loss System - Correlation-Based Dynamic Stop Loss Management
Uses ML model predictions and stock correlations to set intelligent stop loss orders
"""

import asyncio
import numpy as np
import pandas as pd
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass
from enum import Enum

from ibkr_client import IBKRClient
from config import IBKRConfig, DEFAULT_CONFIG

logger = logging.getLogger(__name__)

class StopLossType(Enum):
    """Types of stop loss orders"""
    TRAILING = "trailing"
    FIXED = "fixed"
    CORRELATION_BASED = "correlation_based"
    VOLATILITY_ADJUSTED = "volatility_adjusted"

@dataclass
class StopLossOrder:
    """Stop loss order configuration"""
    ticker: str
    order_type: StopLossType
    stop_price: float
    trail_amount: Optional[float] = None
    correlation_factor: Optional[float] = None
    volatility_factor: Optional[float] = None
    created_at: datetime = None
    last_updated: datetime = None
    
    def __post_init__(self):
        if self.created_at is None:
            self.created_at = datetime.now()
        if self.last_updated is None:
            self.last_updated = datetime.now()

@dataclass
class PositionInfo:
    """Position information for stop loss calculation"""
    ticker: str
    units: int
    purchase_price: float
    current_price: float
    sector: str
    industry: str
    trend_score: float
    predicted_price: float
    volatility: float
    correlation_matches: List[str]
    purchase_date: datetime

class SmartStopLossSystem:
    """
    Intelligent stop loss system that uses:
    1. ML model predictions for dynamic targets
    2. Stock correlations for risk management
    3. Sector/industry analysis for context
    4. Volatility-adjusted thresholds
    """
    
    def __init__(self, ibkr_config: IBKRConfig = None):
        self.config = ibkr_config or DEFAULT_CONFIG
        self.ibkr_client = IBKRClient(self.config)
        self.active_orders: Dict[str, StopLossOrder] = {}
        self.position_history: Dict[str, List[PositionInfo]] = {}
        
        # Configuration parameters
        self.base_stop_loss_pct = 0.15  # 15% base stop loss
        self.base_take_profit_pct = 0.25  # 25% base take profit
        self.correlation_weight = 0.3  # Weight for correlation adjustments
        self.volatility_weight = 0.2  # Weight for volatility adjustments
        self.trend_weight = 0.4  # Weight for trend score adjustments
        self.sector_correlation_threshold = 0.7  # Threshold for sector correlation
        
    async def connect(self) -> bool:
        """Connect to IBKR"""
        return await self.ibkr_client.connect()
    
    def disconnect(self):
        """Disconnect from IBKR"""
        self.ibkr_client.disconnect()
    
    def calculate_smart_stop_loss(self, position: PositionInfo, 
                                 correlation_data: Dict, 
                                 market_data: Dict) -> Tuple[float, float]:
        """
        Calculate intelligent stop loss and take profit levels
        
        Returns:
            Tuple[stop_loss_price, take_profit_price]
        """
        
        # Base calculations
        base_stop = position.purchase_price * (1 - self.base_stop_loss_pct)
        base_profit = position.purchase_price * (1 + self.base_take_profit_pct)
        
        # 1. Trend Score Adjustment
        trend_adjustment = self._calculate_trend_adjustment(position.trend_score)
        
        # 2. Volatility Adjustment
        volatility_adjustment = self._calculate_volatility_adjustment(position.volatility)
        
        # 3. Correlation Adjustment
        correlation_adjustment = self._calculate_correlation_adjustment(
            position, correlation_data, market_data
        )
        
        # 4. Sector Performance Adjustment
        sector_adjustment = self._calculate_sector_adjustment(
            position, market_data
        )
        
        # Combine all adjustments
        total_stop_adjustment = (
            trend_adjustment * self.trend_weight +
            volatility_adjustment * self.volatility_weight +
            correlation_adjustment * self.correlation_weight +
            sector_adjustment * 0.1
        )
        
        total_profit_adjustment = total_stop_adjustment * 0.5  # More conservative on profit
        
        # Apply adjustments
        adjusted_stop = base_stop * (1 + total_stop_adjustment)
        adjusted_profit = base_profit * (1 + total_profit_adjustment)
        
        # Ensure stop loss is below current price and take profit is above
        final_stop = min(adjusted_stop, position.current_price * 0.95)
        final_profit = max(adjusted_profit, position.current_price * 1.05)
        
        logger.info(f"Stop loss calculation for {position.ticker}:")
        logger.info(f"  Base stop: ${base_stop:.2f}, Adjusted: ${final_stop:.2f}")
        logger.info(f"  Base profit: ${base_profit:.2f}, Adjusted: ${final_profit:.2f}")
        logger.info(f"  Adjustments - Trend: {trend_adjustment:.3f}, Vol: {volatility_adjustment:.3f}, Corr: {correlation_adjustment:.3f}")
        
        return final_stop, final_profit
    
    def _calculate_trend_adjustment(self, trend_score: float) -> float:
        """
        Calculate adjustment based on ML model trend score
        Negative trend score (below trend) = tighter stop loss
        Positive trend score (above trend) = looser stop loss
        """
        if trend_score < -2.0:  # Highly below trend - very tight stop
            return -0.3
        elif trend_score < -1.0:  # Below trend - tight stop
            return -0.15
        elif trend_score < 1.0:  # Along trend - normal stop
            return 0.0
        elif trend_score < 2.0:  # Above trend - loose stop
            return 0.15
        else:  # Highly above trend - very loose stop
            return 0.3
    
    def _calculate_volatility_adjustment(self, volatility: float) -> float:
        """
        Calculate adjustment based on stock volatility
        High volatility = wider stop loss to avoid noise
        Low volatility = tighter stop loss
        """
        if volatility > 0.4:  # Very high volatility
            return 0.25
        elif volatility > 0.25:  # High volatility
            return 0.15
        elif volatility > 0.15:  # Medium volatility
            return 0.0
        elif volatility > 0.1:  # Low volatility
            return -0.1
        else:  # Very low volatility
            return -0.2
    
    def _calculate_correlation_adjustment(self, position: PositionInfo, 
                                        correlation_data: Dict, 
                                        market_data: Dict) -> float:
        """
        Calculate adjustment based on correlated stocks performance
        If correlated stocks are performing well = looser stop
        If correlated stocks are performing poorly = tighter stop
        """
        if not position.correlation_matches:
            return 0.0
        
        correlation_performance = []
        
        for corr_ticker in position.correlation_matches:
            if corr_ticker in market_data:
                corr_data = market_data[corr_ticker]
                # Calculate performance of correlated stock
                perf = (corr_data['current_price'] - corr_data['predicted_price']) / corr_data['predicted_price']
                correlation_performance.append(perf)
        
        if not correlation_performance:
            return 0.0
        
        avg_corr_performance = np.mean(correlation_performance)
        
        # If correlated stocks are outperforming predictions, be more optimistic
        if avg_corr_performance > 0.05:  # 5% outperformance
            return 0.2
        elif avg_corr_performance > 0.02:  # 2% outperformance
            return 0.1
        elif avg_corr_performance < -0.05:  # 5% underperformance
            return -0.2
        elif avg_corr_performance < -0.02:  # 2% underperformance
            return -0.1
        else:
            return 0.0
    
    def _calculate_sector_adjustment(self, position: PositionInfo, market_data: Dict) -> float:
        """
        Calculate adjustment based on sector performance
        """
        sector_stocks = [
            ticker for ticker, data in market_data.items() 
            if data.get('sector') == position.sector and ticker != position.ticker
        ]
        
        if len(sector_stocks) < 3:  # Not enough sector data
            return 0.0
        
        sector_performance = []
        for ticker in sector_stocks[:10]:  # Limit to 10 stocks for performance
            data = market_data[ticker]
            perf = (data['current_price'] - data['predicted_price']) / data['predicted_price']
            sector_performance.append(perf)
        
        avg_sector_performance = np.mean(sector_performance)
        
        # Sector momentum adjustment
        if avg_sector_performance > 0.03:
            return 0.1
        elif avg_sector_performance < -0.03:
            return -0.1
        else:
            return 0.0
    
    async def create_stop_loss_orders(self, positions: List[PositionInfo], 
                                    correlation_data: Dict, 
                                    market_data: Dict) -> Dict[str, StopLossOrder]:
        """
        Create intelligent stop loss orders for all positions
        """
        new_orders = {}
        
        for position in positions:
            try:
                stop_price, profit_price = self.calculate_smart_stop_loss(
                    position, correlation_data, market_data
                )
                
                # Determine best stop loss type based on position characteristics
                stop_type = self._determine_stop_loss_type(position)
                
                # Create stop loss order
                order = StopLossOrder(
                    ticker=position.ticker,
                    order_type=stop_type,
                    stop_price=stop_price,
                    trail_amount=self._calculate_trail_amount(position) if stop_type == StopLossType.TRAILING else None,
                    correlation_factor=self._calculate_correlation_factor(position, correlation_data),
                    volatility_factor=position.volatility
                )
                
                new_orders[position.ticker] = order
                
                # Place the actual order with IBKR
                if await self._place_stop_loss_order(order, position):
                    logger.info(f"✅ Stop loss order placed for {position.ticker} at ${stop_price:.2f}")
                else:
                    logger.error(f"❌ Failed to place stop loss order for {position.ticker}")
                
            except Exception as e:
                logger.error(f"Error creating stop loss for {position.ticker}: {e}")
        
        self.active_orders.update(new_orders)
        return new_orders
    
    def _determine_stop_loss_type(self, position: PositionInfo) -> StopLossType:
        """Determine the best stop loss type for a position"""
        if position.volatility > 0.3:
            return StopLossType.VOLATILITY_ADJUSTED
        elif len(position.correlation_matches) > 2:
            return StopLossType.CORRELATION_BASED
        elif position.trend_score > 1.0:  # Strong uptrend
            return StopLossType.TRAILING
        else:
            return StopLossType.FIXED
    
    def _calculate_trail_amount(self, position: PositionInfo) -> float:
        """Calculate trailing amount based on volatility"""
        base_trail = position.current_price * 0.05  # 5% base trail
        volatility_adjustment = position.volatility * 0.1
        return base_trail * (1 + volatility_adjustment)
    
    def _calculate_correlation_factor(self, position: PositionInfo, correlation_data: Dict) -> float:
        """Calculate correlation strength factor"""
        if not position.correlation_matches:
            return 0.0
        return min(len(position.correlation_matches) / 5.0, 1.0)  # Max factor of 1.0
    
    async def _place_stop_loss_order(self, order: StopLossOrder, position: PositionInfo) -> bool:
        """Place the actual stop loss order with IBKR"""
        try:
            # Use IBKR client to place stop loss order
            result = await self.ibkr_client.place_stop_loss_order(
                symbol=order.ticker,
                quantity=position.units,
                stop_price=order.stop_price,
                action="SELL"
            )
            return result is not None
        except Exception as e:
            logger.error(f"Failed to place stop loss order for {order.ticker}: {e}")
            return False
    
    async def update_stop_loss_orders(self, current_positions: List[PositionInfo], 
                                    correlation_data: Dict, 
                                    market_data: Dict):
        """Update existing stop loss orders based on new market data"""
        for position in current_positions:
            if position.ticker in self.active_orders:
                order = self.active_orders[position.ticker]
                
                # Recalculate stop loss
                new_stop, new_profit = self.calculate_smart_stop_loss(
                    position, correlation_data, market_data
                )
                
                # Update if significant change
                if abs(new_stop - order.stop_price) / order.stop_price > 0.05:  # 5% change threshold
                    await self._modify_stop_loss_order(order, new_stop)
                    order.stop_price = new_stop
                    order.last_updated = datetime.now()
    
    async def _modify_stop_loss_order(self, order: StopLossOrder, new_stop_price: float):
        """Modify existing stop loss order"""
        try:
            await self.ibkr_client.modify_stop_loss_order(order.ticker, new_stop_price)
            logger.info(f"Updated stop loss for {order.ticker} to ${new_stop_price:.2f}")
        except Exception as e:
            logger.error(f"Failed to modify stop loss for {order.ticker}: {e}")
    
    def get_stop_loss_summary(self) -> Dict:
        """Get summary of all active stop loss orders"""
        summary = {
            'total_orders': len(self.active_orders),
            'orders_by_type': {},
            'orders': []
        }
        
        for order in self.active_orders.values():
            order_type = order.order_type.value
            summary['orders_by_type'][order_type] = summary['orders_by_type'].get(order_type, 0) + 1
            summary['orders'].append({
                'ticker': order.ticker,
                'type': order_type,
                'stop_price': order.stop_price,
                'created': order.created_at,
                'updated': order.last_updated
            })
        
        return summary
